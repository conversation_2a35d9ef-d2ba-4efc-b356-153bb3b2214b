/**
 * 文件上传队列管理器
 * 支持并发控制、分批处理、优先级管理等功能
 */

export interface UploadTask {
  id: string;
  file: File;
  options: any;
  priority?: number;
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  error?: Error;
  xhr?: XMLHttpRequest;
  onProgress?: (progress: number) => void;
  onSuccess?: (response: any) => void;
  onError?: (error: Error) => void;
  retryCount?: number;
}

export interface UploadQueueOptions {
  maxConcurrent?: number; // 最大并发数
  batchSize?: number; // 批处理大小
  retryLimit?: number; // 重试次数限制
  timeout?: number; // 超时时间
  onBatchComplete?: (batch: UploadTask[]) => void;
  onQueueComplete?: () => void;
  onQueueProgress?: (progress: { completed: number; total: number; percentage: number }) => void;
}

export class UploadQueue {
  private tasks: Map<string, UploadTask> = new Map();
  private pendingTasks: UploadTask[] = [];
  private activeTasks: Set<UploadTask> = new Set();
  private completedTasks: UploadTask[] = [];
  private failedTasks: UploadTask[] = [];
  
  private options: Required<UploadQueueOptions>;
  private isProcessing = false;
  private isPaused = false;

  constructor(options: UploadQueueOptions = {}) {
    this.options = {
      maxConcurrent: 3, // 默认最大并发3个
      batchSize: 10, // 默认批处理10个
      retryLimit: 2, // 默认重试2次
      timeout: 60000, // 默认60秒超时
      onBatchComplete: () => {},
      onQueueComplete: () => {},
      onQueueProgress: () => {},
      ...options,
    };
  }

  /**
   * 添加上传任务
   */
  addTask(task: Omit<UploadTask, 'status' | 'progress' | 'retryCount'>): string {
    const uploadTask: UploadTask = {
      ...task,
      status: 'pending',
      progress: 0,
      retryCount: 0,
      priority: task.priority || 0,
    };

    this.tasks.set(task.id, uploadTask);
    this.pendingTasks.push(uploadTask);
    
    // 按优先级排序
    this.pendingTasks.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // 如果队列没有在处理，开始处理
    if (!this.isProcessing && !this.isPaused) {
      this.processQueue();
    }

    return task.id;
  }

  /**
   * 批量添加任务
   */
  addTasks(tasks: Omit<UploadTask, 'status' | 'progress' | 'retryCount'>[]): string[] {
    const taskIds: string[] = [];
    
    tasks.forEach(task => {
      const uploadTask: UploadTask = {
        ...task,
        status: 'pending',
        progress: 0,
        retryCount: 0,
        priority: task.priority || 0,
      };

      this.tasks.set(task.id, uploadTask);
      this.pendingTasks.push(uploadTask);
      taskIds.push(task.id);
    });

    // 按优先级排序
    this.pendingTasks.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // 如果队列没有在处理，开始处理
    if (!this.isProcessing && !this.isPaused) {
      this.processQueue();
    }

    return taskIds;
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) return false;

    if (task.status === 'uploading' && task.xhr) {
      task.xhr.abort();
    }

    task.status = 'cancelled';
    this.activeTasks.delete(task);
    
    // 从待处理队列中移除
    const pendingIndex = this.pendingTasks.findIndex(t => t.id === taskId);
    if (pendingIndex !== -1) {
      this.pendingTasks.splice(pendingIndex, 1);
    }

    return true;
  }

  /**
   * 暂停队列
   */
  pause(): void {
    this.isPaused = true;
  }

  /**
   * 恢复队列
   */
  resume(): void {
    this.isPaused = false;
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  /**
   * 清空队列
   */
  clear(): void {
    // 取消所有活动任务
    this.activeTasks.forEach(task => {
      if (task.xhr) {
        task.xhr.abort();
      }
    });

    this.tasks.clear();
    this.pendingTasks = [];
    this.activeTasks.clear();
    this.completedTasks = [];
    this.failedTasks = [];
    this.isProcessing = false;
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    const total = this.tasks.size;
    const completed = this.completedTasks.length;
    const failed = this.failedTasks.length;
    const uploading = this.activeTasks.size;
    const pending = this.pendingTasks.length;

    return {
      total,
      completed,
      failed,
      uploading,
      pending,
      percentage: total > 0 ? Math.round((completed / total) * 100) : 0,
      isProcessing: this.isProcessing,
      isPaused: this.isPaused,
    };
  }

  /**
   * 获取任务详情
   */
  getTask(taskId: string): UploadTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): UploadTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 重试失败的任务
   */
  retryFailedTasks(): void {
    const failedTasks = [...this.failedTasks];
    this.failedTasks = [];

    failedTasks.forEach(task => {
      if ((task.retryCount || 0) < this.options.retryLimit) {
        task.status = 'pending';
        task.progress = 0;
        task.error = undefined;
        task.retryCount = (task.retryCount || 0) + 1;
        this.pendingTasks.push(task);
      } else {
        this.failedTasks.push(task);
      }
    });

    // 重新排序
    this.pendingTasks.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    if (!this.isProcessing && !this.isPaused) {
      this.processQueue();
    }
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.isPaused) return;

    this.isProcessing = true;

    while (this.pendingTasks.length > 0 && !this.isPaused) {
      // 控制并发数量
      while (this.activeTasks.size < this.options.maxConcurrent && this.pendingTasks.length > 0) {
        const task = this.pendingTasks.shift();
        if (task) {
          this.activeTasks.add(task);
          this.uploadTask(task);
        }
      }

      // 等待至少一个任务完成
      if (this.activeTasks.size > 0) {
        await this.waitForTaskCompletion();
      }
    }

    this.isProcessing = false;

    // 检查是否所有任务都完成
    if (this.pendingTasks.length === 0 && this.activeTasks.size === 0) {
      this.options.onQueueComplete();
    }
  }

  /**
   * 等待任务完成
   */
  private waitForTaskCompletion(): Promise<void> {
    return new Promise((resolve) => {
      const checkCompletion = () => {
        if (this.activeTasks.size < this.options.maxConcurrent || this.activeTasks.size === 0) {
          resolve();
        } else {
          setTimeout(checkCompletion, 100);
        }
      };
      checkCompletion();
    });
  }

  /**
   * 上传单个任务
   */
  private uploadTask(task: UploadTask): void {
    task.status = 'uploading';
    
    const { file, options } = task;
    const { onProgress, onSuccess, onError } = options;

    const formData = new FormData();
    formData.append('file', file);
    
    // 添加其他参数
    Object.keys(options.data || {}).forEach(key => {
      formData.append(key, options.data[key]);
    });

    const xhr = new XMLHttpRequest();
    task.xhr = xhr;

    // 上传进度
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percent = Math.round((event.loaded / event.total) * 100);
        task.progress = percent;
        task.onProgress?.(percent);
        onProgress?.({ percent });
        this.updateQueueProgress();
      }
    });

    // 上传完成
    xhr.addEventListener('load', () => {
      this.activeTasks.delete(task);
      
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          if (response.code === 200) {
            task.status = 'completed';
            task.progress = 100;
            this.completedTasks.push(task);
            task.onSuccess?.(response);
            onSuccess?.(response);
          } else {
            this.handleTaskError(task, new Error(response.message || '上传失败'));
            onError?.(task.error!);
          }
        } catch (error) {
          this.handleTaskError(task, new Error('响应解析失败'));
          onError?.(task.error!);
        }
      } else {
        this.handleTaskError(task, new Error(`上传失败，状态码: ${xhr.status}`));
        onError?.(task.error!);
      }

      this.updateQueueProgress();
    });

    // 网络错误
    xhr.addEventListener('error', () => {
      this.activeTasks.delete(task);
      this.handleTaskError(task, new Error('网络错误，请检查网络连接'));
      onError?.(task.error!);
      this.updateQueueProgress();
    });

    // 上传取消
    xhr.addEventListener('abort', () => {
      this.activeTasks.delete(task);
      if (task.status !== 'cancelled') {
        task.status = 'cancelled';
      }
    });

    // 超时处理
    xhr.timeout = this.options.timeout;
    xhr.addEventListener('timeout', () => {
      this.activeTasks.delete(task);
      this.handleTaskError(task, new Error('上传超时，请重试'));
      onError?.(task.error!);
      this.updateQueueProgress();
    });

    // 发送请求
    xhr.open('POST', options.action);
    
    // 设置请求头
    if (options.headers) {
      Object.keys(options.headers).forEach(key => {
        if (options.headers[key]) {
          xhr.setRequestHeader(key, options.headers[key]);
        }
      });
    }

    xhr.send(formData);
  }

  /**
   * 处理任务错误
   */
  private handleTaskError(task: UploadTask, error: Error): void {
    task.error = error;
    task.status = 'failed';
    this.failedTasks.push(task);
  }

  /**
   * 更新队列进度
   */
  private updateQueueProgress(): void {
    const status = this.getStatus();
    this.options.onQueueProgress(status);
  }
}
