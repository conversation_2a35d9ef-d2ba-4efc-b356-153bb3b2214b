import Footer from '@/components/Footer';
import RightContent from '@/components/RightContent';
import type { MenuDataItem, Settings as LayoutSettings } from '@ant-design/pro-layout';
import { Input, message } from 'antd';
import qs from 'qs';
import React from 'react';
// import { isExternalNetwork } from '@/utils/utils';
import type { RequestConfig, RequestInterceptorUmiRequest } from '@umijs/max';
import { history } from '@umijs/max';
import { autoFixContext } from 'react-activation';
import jsxDevRuntime from 'react/jsx-dev-runtime';
import jsxRuntime from 'react/jsx-runtime';
import defaultSettings from '../config/defaultSettings';
import { addPath } from './components/HeaderTab/getTabsList';
import { fetchDevAccess, getBizSSOGrayInfo } from './pages/DevConfig/service';
import './reset.css';
import type { API } from './services/API';
import { ssoLogin } from './services/sso';
import { queryCurrent, querySSOCurrent } from './services/user';
// import { message } from 'antd';
// import { ResponseError } from 'umi-request';
// import * as Sentry from '@sentry/react';

import { setGlobalUserInfo } from './global';
import { getCurrentGrayStatus } from './services/global';
import {
  filterSSOQueryParams,
  getLoginType,
  getPassportName,
  getSSOCookie,
  getToken,
  removeLoginStorage,
  removeSSOCookie,
  removeSSOVanTaskID,
  setDevXHllGrayVersionHeaderByStorage,
  setLoginType,
  setPassportName,
  setPid,
  setSSOAPICookie,
  setSSOCookie,
  setSSOVanTaskID,
  setToken,
  SSOGrayVersionHeader,
  SYSTEM_FLAG_HEADER,
  SYSTEM_FLAG_HEADER_VALUE,
  toAuth,
  toRemoveAuth,
  xHllGrayVersionHeader,
} from './utils/auth';
import { sendSensitiveData, sendSensitiveDataByPage } from './utils/sensitiveDataUtils';
import { deepTrimParams } from './utils/tools';
import { getBaseUrl, getTheme, getVanEvn, isExternalNetwork, isStandbyLogin } from './utils/utils';
// import { isExternalNetwork } from '@/utils/utils';

// 修复keepalive报错
autoFixContext([jsxRuntime, 'jsx', 'jsxs', 'jsxDEV'], [jsxDevRuntime, 'jsx', 'jsxs', 'jsxDEV']);

export async function getInitialState(): Promise<{
  settings?: LayoutSettings;
  currentUser?: API.CurrentUser;
  globalEnum?: API.GlobalEnum;
  fetchUserInfo: () => Promise<API.CurrentUser | undefined>;
  // fetchGlobalEnum: () => Promise<API.GlobalEnum | undefined>;
}> {
  // const fetchUserInfo = async () => {
  //   try {
  //     const { data } = await queryCurrent();
  //     return data;
  //   } catch (error) {
  //     removeToken();
  //     history.push('/user/login');
  //   }
  //   return undefined;
  // };
  //
  // 是否开启前端灰度版本
  let switchOfSSOGrayVersion: boolean = false;
  const initSSOGrayVersion = async () => {
    if (getVanEvn() !== 'prd') return;
    const passportName = getPassportName();
    if (isExternalNetwork() || !passportName) return;
    let grayHeader: any = {};
    if (passportName) {
      // 登录方式为sso，且有passportName，从bike获取灰度计划
      const res = await getBizSSOGrayInfo().catch(() => false);
      if (res && res?.data && res?.data?.list) {
        // 按优先级升序
        const sortedList = res.data.list.sort((a: any, b: any) => a.priority - b.priority);
        const grayVersion =
          sortedList.find((item: any) => item?.userList?.includes(passportName)) || {};
        const { vanTaskId: vanTaskIdFromDataset } = document.documentElement.dataset;
        if (grayVersion?.valid) {
          // 内网 && 灰度配置名单包含当前passportName && 灰度版本有效（valid=true）
          // 匹配到用户的第一个灰度计划，关闭时，不再匹配后续灰度计划，前后端灰度均关闭
          switchOfSSOGrayVersion = true;
          if (grayVersion.vanTaskId && grayVersion.vanTaskId === vanTaskIdFromDataset) {
            setSSOVanTaskID(grayVersion.vanTaskId);
            console.log('vanTaskId', grayVersion.vanTaskId);
          }
        } else {
          removeSSOVanTaskID();
        }
        if (
          grayVersion &&
          grayVersion?.grayHeaderKey &&
          grayVersion?.grayHeaderValue &&
          grayVersion?.valid &&
          grayVersion?.vanTaskId === vanTaskIdFromDataset
        ) {
          grayHeader = {
            [grayVersion.grayHeaderKey]: grayVersion.grayHeaderValue,
          };
          SSOGrayVersionHeader.setValue(grayHeader);
        }
      }
    }
    // 存到闭包中，供请求拦截器使用
  };

  const initDevAccess = async () => {
    if (isExternalNetwork()) return [];
    const res: any = await fetchDevAccess().catch(() => false);
    if (res && res?.data && res?.data?.list) {
      return res?.data?.list;
    }
    return [];
  };
  const fetchUserInfo = async () => {
    // van sso灰度信息配置权限白名单
    const devAccessWhiteList: string[] = await initDevAccess().catch(() => []);
    //
    try {
      if (isStandbyLogin()) {
        const { data } = await queryCurrent();
        return data;
      } else {
        const { data } = await querySSOCurrent();
        return {
          role: data.roleList || undefined,
          access: data.privilegeList || undefined,
          accountName: data.employeeName,
          channelCode: data.channelCode,
          channelName: data.channelName,
          channelType: data.channelType,
          channelLevel: data.channelLevel,
          parentChannelCode: data.parentChannelCode,
          userId: data.pid,
          userName: data.employeeName,
          employeeType: data.employeeType,
          pid: data?.pid,
          token: '',
          extSource: data?.extSource,
          // van sso灰度相关
          passportName: getPassportName(),
          devAccessWhiteList,
        };
      }
    } catch (error) {
      // removeToken();
      // history.push('/user/login');
    }
    return undefined;
  };

  // 检测是否是sso授权后回调页面
  const { searchParams } = new URL(window.location.href);
  try {
    if (searchParams.has('identifier') && searchParams.has('account')) {
      removeLoginStorage();
      // 储存飞书id、sso cookie，供van sso灰度使用
      const passportName: string = searchParams.getAll('account')[
        searchParams.getAll('account')?.length - 1
      ] as string;
      const ssoCookieVal: string = searchParams.getAll('identifier')[
        searchParams.getAll('identifier')?.length - 1
      ] as string;
      setPassportName(passportName);
      // 存储sso cookie值，供接口使用
      setSSOAPICookie(ssoCookieVal);
      // 查询是否有灰度配置
      await initSSOGrayVersion().catch(() => false);
      if (!switchOfSSOGrayVersion || isExternalNetwork()) {
        // 无灰度配置、或灰度配置不生效(valid=false)、或是外网
        // 同时禁用前端、后端灰度
        removeSSOCookie();
        SSOGrayVersionHeader.setValue({});
      } else {
        // 校验是否已经有sso cookie
        // 灰度配置生效，且是内网
        // eslint-disable-next-line no-lonely-if
        if (!getSSOCookie() || getSSOCookie() !== ssoCookieVal) {
          setSSOCookie(ssoCookieVal);
          // window.location.reload();
          // 刷新页面, 新cookie会辅助van判断，是否命中van sso灰度，加载灰度版本的前端资源
          if (getSSOCookie() && getSSOCookie() === ssoCookieVal) {
            window.location.reload();
          }
        }
      }

      // sso授权返回，根据sso回调返回的query参数初始化登录态
      await ssoLogin({
        identifier: searchParams.getAll('identifier')[
          searchParams.getAll('identifier')?.length - 1
        ] as string,
        extAccount: searchParams.getAll('account')[
          searchParams.getAll('account')?.length - 1
        ] as string,
      })
        .then((res) => {
          console.log('ssoLogin', res);
          const { token, pid } = res.data;
          setToken(token);
          setLoginType();
          setPid(pid);
        })
        .then(() => {
          // 移除sso登录相关的query
          const target = filterSSOQueryParams(window.location.href);
          const { pathname, search } = new URL(target);
          history.replace(pathname + search);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  } catch (err) {
    console.log(err);
  }
  // 再次初始化sso灰度信息，处理内存丢失，导致灰度信息缺失问题
  await initSSOGrayVersion().catch(() => false);

  // 如果是登录页面，不执行
  if (history.location.pathname !== '/user/login') {
    if (getToken() === '') {
      message.error('登录已过期，请重新登录；');
      // history.push('/user/login');
      toAuth();
      return {
        fetchUserInfo,
        settings: defaultSettings,
      };
    }

    // const { data: globalEnum } = await getGlobalEnum();
    const currentUser = await fetchUserInfo();
    setGlobalUserInfo(currentUser); //  存储全局用户信息

    const grayUserInfo = await getCurrentGrayStatus(
      currentUser?.pid,
      currentUser?.employeeType,
    ).catch(() => []); // data 为true 表示属于灰度用户
    return {
      fetchUserInfo,
      // globalEnum,
      currentUser: {
        ...currentUser,
        isGrayUser: grayUserInfo?.data,
      },
      settings: defaultSettings,
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings,
  };
}
// 过滤
const filterByMenuDate = (data: MenuDataItem[], keyWord: string): MenuDataItem[] =>
  data
    .map((item) => {
      if (
        (item.name && item.name.includes(keyWord)) ||
        filterByMenuDate(item.children || [], keyWord).length > 0
      ) {
        return {
          ...item,
          children:
            filterByMenuDate(item.children || [], keyWord).length > 0
              ? filterByMenuDate(item.children || [], keyWord)
              : item.children || [],
        };
      }
      return undefined;
    })
    .filter((item) => item) as MenuDataItem[];

// const
// 展开筛选后的菜单项
const filterOpenKeysByMenuData = (data: MenuDataItem[], value: string): string[] => {
  return (
    (value &&
      data.map((item) => {
        if (item?.children) {
          return [item?.key, filterOpenKeysByMenuData(item.children, value)];
        }
        return undefined;
      })) ||
    []
  )
    .flat(Infinity)
    .filter((item) => item) as string[];
};

export const layout = ({
  initialState,
  setInitialState,
}: {
  initialState: {
    settings?: LayoutSettings;
    currentUser?: API.CurrentUser;
    globalEnum?: API.GlobalEnum;
    menus?: MenuDataItem[];
    openKeys?: string[];
  };
  setInitialState: any;
}) => {
  return {
    token: getTheme({
      realDark: {
        sider: {
          colorMenuBackground: '#000',
        },
      },
    }),
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    footerRender: () => <Footer />,
    locale: 'zh-CN',
    onPageChange: () => {
      const { currentUser } = initialState;
      const { location } = history;
      const { tabName, openNew } = location.query as {
        tabName?: string;
        openNew?: boolean;
      };
      if (location.pathname !== '/user/login' && location.pathname !== '/dashboard') {
        addPath(location, tabName, !!openNew);
      }
      // 如果没有登录，重定向到 login
      if (!currentUser) {
        // history.push('/user/login');
        toAuth();
      }
      // 敏感数据日志上报-页面层级
      sendSensitiveDataByPage();
    },
    postMenuData: (menus: MenuDataItem[]) => initialState.menus! || menus,
    itemRender: (route: { breadcrumbName: string }) => <span>{route.breadcrumbName}</span>,
    menuHeaderRender: () => undefined,
    menuExtraRender: ({
      collapsed,
      menuData,
    }: {
      collapsed: boolean;
      menuData: MenuDataItem[];
    }) => {
      return (
        !collapsed && (
          <Input.Search
            allowClear
            onSearch={(value) => {
              const menus = filterByMenuDate(menuData || [], value || '');
              const openKeys = filterOpenKeysByMenuData(menus, value);
              setInitialState({
                ...initialState,
                menus,
                openKeys,
              });
            }}
          />
        )
      );
    },

    menuProps: {
      openKeys: initialState?.openKeys,
      onOpenChange: (value: string[]) => {
        setInitialState({ ...initialState, openKeys: value });
      },
    },
    // 监听菜单收放事件
    onCollapse: (collapsed: boolean) => {
      document.body.classList.toggle('side-bar-collapse', collapsed);
    },
    // menu: { defaultOpenAll: true },
    ...initialState?.settings,
  };
};

const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  405: '请求方法不被允许。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

// 统一设置多版本请求头，其他不被拦截器处理的请求（主要是文件上传接口）统一引用此处设置的多版本请求头
// pre、stg分开设置，提交release/生产时，留意删除多版本！
// xHllGrayVersionHeader.setValue({
//   pre: {
//     // 'x-hll-gray-version': 'v67043',
//   },
// });

// 代码写死多版本不再推荐！
// pre、localhost环境移步至: 页面右上角工具栏【多版本管理】进行设置
try {
  // 根据localStorage中设置的多版本信息，初始化预发环境多版本请求头
  setDevXHllGrayVersionHeaderByStorage();
} catch (err) {
  console.log('setDevXHllGrayVersionHeaderByStorage', err);
}

// 请求拦截器:
const requestHandler: RequestInterceptorUmiRequest = (url, options) => {
  // sensitiveSwitch 敏感数据日志上报开关
  const { ifTrimParams, sensitiveSwitch } = options;
  if (ifTrimParams) {
    const paramsKey = options.method?.toLowerCase() === 'get' ? 'params' : 'data';
    options[paramsKey] = deepTrimParams(options[paramsKey]);
  }
  // 敏感数据日志异步上报-接口层级
  if (sensitiveSwitch) {
    sendSensitiveData(options);
  }
  return {
    url,
    options: {
      ...options,
      // 序列化数组参数
      paramsSerializer: (params) => {
        return qs.stringify(params, { indices: false });
      },
      headers: {
        ...options.headers,
        // sso 灰度请求头
        ...SSOGrayVersionHeader.getValue(),
        authorization: getToken(),
        loginType: getLoginType(),
        ...xHllGrayVersionHeader.getValue(),

        // api系统标识，提供给服务端用于区分不同系统的请求
        // key=system-flag
        [SYSTEM_FLAG_HEADER]: SYSTEM_FLAG_HEADER_VALUE,
      },
    },
  };
};

const responseHandler = async (response: any) => {
  // skipGlobalErrorTip 这个属性 跳过全局错误提示,是传的一个自定义属性，用于跳过全局错误提示，使其在具体代码的中catch中捕获提示，以便做一些自定义错误处理
  const { config, status } = response;
  const { skipGlobalErrorTip, url } = config;

  // http 异常拦截
  if (status !== 200) {
    if (status === 401 && window.location.pathname !== '/user/login') {
      if (
        !(
          url?.includes('/auth/operator/login/logout') ||
          url?.includes('/bizadmin/employee/SSOLogout')
        )
      ) {
        toRemoveAuth();
      } else {
        toAuth();
      }
    }
    message.error(codeMessage[status as keyof typeof codeMessage]);
    return Promise.reject(new Error(codeMessage[status as keyof typeof codeMessage]));
  }
  // 下载文件流
  if (
    status === 200 &&
    [
      'application/vnd.ms-excel;charset=utf-8',
      'application/octet-stream;charset=UTF-8',
      'application/zip;charset=UTF-8',
    ].includes(response.headers['content-type'])
  ) {
    return Promise.resolve(response);
  }

  // 业务拦截处理
  // 处理blob数据
  if (response.data instanceof Blob && response.data?.type === 'application/json') {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsText(response.data, 'utf-8');
      reader.onload = () => {
        const resp = JSON.parse(reader.result as string);
        const { ret, msg, code } = resp;
        // token过期，去登录
        if (status === 200 && (ret === 500015 || ret === 2)) {
          toAuth();
        }
        if (ret === 200 || ret === 0 || code === 200) {
          resp.success = true;
          return resolve(response);
        }
        // API鉴权失败，无当前接口权限
        if (status === 200 && ret === 502009) {
          message.error(msg);
          history.replace('/dashboard');
        }
        if (!skipGlobalErrorTip) {
          message.error(msg);
        }
        // eslint-disable-next-line prefer-promise-reject-errors
        resp.success = false;
        return reject(response);
      };
    });
  } else {
    const { ret, data, msg, code } = response.data;
    // token过期，去登录
    if (status === 200 && (ret === 500015 || ret === 2)) {
      toAuth();
    }
    // API鉴权失败，无当前接口权限
    if (status === 200 && ret === 502009) {
      message.error(msg);
      history.replace('/dashboard');
    }
    if (ret === 200 || ret === 0 || code === 200) {
      if (data && Object.prototype.hasOwnProperty.call(data, 'pageSize')) {
        // 分页数据单独处理
        return Promise.resolve({ data, success: true });
      }
      response.data.success = true;
      return Promise.resolve(response);
    }
    if (!skipGlobalErrorTip) {
      message.error(msg);
    }
    response.data.success = false;
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject({ ...response.data });
  }
};

export const request: RequestConfig = {
  timeout: 300000,
  baseURL: getBaseUrl(),
  validateStatus: (status) => {
    return !!status;
  },
  requestInterceptors: [requestHandler],
  responseInterceptors: [responseHandler],
  errorConfig: {
    errorHandler: (error) => {
      throw error;
    },
  },
};
