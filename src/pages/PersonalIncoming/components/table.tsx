import { CLASSIFICATION_CODE_LABEL, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { disableFutureDate } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess } from '@umijs/max';
import { Tooltip } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React, { useRef, useState } from 'react';

import SensitiveData from '@/components/SensitiveDataView';
import { CUSTOMER_TYPE_MAP } from '@/pages/BusinessCashMng/const';
import { exPrivateStatusMap, merchantStatusMap, privateStatusMap } from '../const';
import type { UserInputRecordListItem } from '../data';
import { getInputRecordList, incomeExport } from '../service';
import LogOff from './LogOff';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps, removeBlankFromObject } from '@/utils/tools';

// 解决日期范围选择器默认值报错
dayjs.extend(weekday);
dayjs.extend(localeData);

const RecordTable: React.FC<{}> = () => {
  const currentDate = dayjs();
  const defaultStartDate = dayjs().subtract(1, 'month');
  const [firstLoad, setFirstLoad] = useState(true);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [statusOptions, setStatusOption] = useState<{
    label: string;
    value: string;
  }>();
  const optionsMap = (mapStatus: any) => {
    return Object.keys(mapStatus).map((item) => {
      return {
        value: item,
        label: mapStatus[item],
      };
    });
  };

  // 当前登陆用户信息
  const access = useAccess();

  const getSearchParams = () => {
    const {
      riskStartTime,
      registerTime,
      creditEndTime,
      secondTypeCode,
      createAt,
      ...data
    } = formRef?.current?.getFieldsValue();
    let newForm = { ...data };
    let startRiskStartTime;
    let endRiskStartTime;
    let startRegisterTime;
    let endRegisterTime;
    let startCreditEndTime;
    let endCreditEndTime;
    let startCreatedAt;
    let endCreatedAt;
    let secondCodeAndHistory = {};
    if (createAt?.length) {
      startCreatedAt = `${createAt[0].format('YYYY-MM-DD')} 00:00:00`;
      endCreatedAt = `${createAt[1].format('YYYY-MM-DD')} 23:59:59`;
    }
    if (riskStartTime?.length) {
      startRiskStartTime = `${riskStartTime[0].format('YYYY-MM-DD')} 00:00:00`;
      endRiskStartTime = `${riskStartTime[1].format('YYYY-MM-DD')} 23:59:59`;
    }
    if (registerTime?.length) {
      startRegisterTime = `${registerTime[0].format('YYYY-MM-DD')} 00:00:00`;
      endRegisterTime = `${registerTime[1].format('YYYY-MM-DD')} 23:59:59`;
    }
    if (creditEndTime?.length) {
      startCreditEndTime = `${creditEndTime[0].format('YYYY-MM-DD')} 00:00:00`;
      endCreditEndTime = `${creditEndTime[1].format('YYYY-MM-DD')} 23:59:59`;
    }
    console.log(secondTypeCode);
    if (secondTypeCode === 'HISTORY_CASH') {
      secondCodeAndHistory = {
        history030101Income: true,
        secondTypeCode: SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
      };
    } else {
      secondCodeAndHistory = { secondTypeCode };
    }
    newForm = {
      ...data,
      startRiskStartTime,
      endRiskStartTime,
      startRegisterTime,
      endRegisterTime,
      startCreditEndTime,
      endCreditEndTime,
      startCreatedAt,
      endCreatedAt,
      ...secondCodeAndHistory,
    };
    return removeBlankFromObject(filterProps(newForm));
  };

  // 默认日期选择器快捷选项
  const defaultTimeRangeShortCut = {
    最近半年: [currentDate.subtract(6, 'month'), currentDate],
    最近一年: [currentDate.subtract(1, 'year'), currentDate],
    最近两年: [currentDate.subtract(2, 'year'), currentDate],
  };
  const columns: ProColumns<UserInputRecordListItem>[] = [
    {
      title: '进件流水号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      search: false,
      render: (_, row) => (
        <>
          <Link to={`/userMng/personalMng/detail?orderNo=${row?.orderNo}`}>{row?.orderNo}</Link>
        </>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createAt',
      initialValue: [defaultStartDate, currentDate],
      valueType: 'dateRange',
      fieldProps: {
        ranges: defaultTimeRangeShortCut,
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.createAt;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => {
          if (typeof value[0] !== 'string') {
            return {
              startCreatedAt: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
              endCreatedAt: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
            };
          }
          return {
            startCreatedAt: `${value[0].split(' ')[0]} 00:00:00`,
            endCreatedAt: `${value[1].split(' ')[0]} 23:59:59`,
          };
        },
      },
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      key: 'userId',
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      minWidth: 130,
      key: 'phone',
      render: (_, record) => {
        if (!record.phone) return '-';
        return (
          <SensitiveData
            value={record.phone}
            fetchRawValue={{ code: record.checkCode, data: record.phoneEncrypted }}
          />
        );
      },
    },
    {
      title: '证件号码',
      dataIndex: 'idNo',
      minWidth: 182,
      key: 'idNo',
      render: (_, record) => {
        if (!record.idNo) return '-';
        return (
          <SensitiveData
            value={record.idNo}
            fetchRawValue={{ code: record.checkCode, data: record.idNoEncrypted }}
          />
        );
      },
    },
    {
      title: '用户类型',
      dataIndex: 'customerType',
      valueEnum: CUSTOMER_TYPE_MAP,
    },
    {
      title: '资金渠道',
      dataIndex: 'channelCode',
      valueEnum: {
        BAI_XIN: '百信',
        YI_REN_XING: '易人行',
        LE_XIANG_JIE: '乐享借',
        WEI_XIN_JIN_KE: '维信金科',
        MA_SHANG_XIAO_FEI: '马上消费',
        XIAO_YING_KA_DAI: '小赢卡贷',
        TIAN_CHEN_JIN_RONG: '甜橙金融',
        FEI_QUAN: '飞泉',
        JING_DONG_YUN_GONG_CHANG: '京银融',
        HUO_SHAN_RONG: '火山融',
      },
      hideInTable: true,
    },
    {
      title: '资金渠道',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      key: 'classification',
      order: 2,
      valueEnum: CLASSIFICATION_CODE_LABEL,
      fieldProps: {
        onChange: () => {
          formRef.current?.setFieldValue('secondTypeCode', undefined);
        },
      },
      // search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondTypeName',
      key: 'secondTypeName',
      search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondTypeCode',
      key: 'secondTypeCode',
      order: 1,
      valueEnum: () => {
        // 每次表单改变就会触发
        const classificationCode = formRef.current?.getFieldValue('classification');
        const leaseCodeMap = {
          '0201': '小圆车融',
        };
        const loanCodeMap = {
          '0301': '圆易借',
          '0304': '圆商贷',
          HISTORY_CASH: '易人行（历史）',
        };
        const codeMap = {
          '02': leaseCodeMap,
          '03': loanCodeMap,
        };
        return codeMap[classificationCode]
          ? codeMap[classificationCode]
          : { ...leaseCodeMap, ...loanCodeMap };
      },

      hideInTable: true,
      fieldProps: {
        onChange: (val: string) => {
          const code = val === 'HISTORY_CASH' ? '0301' : val;
          formRef.current?.setFieldValue('classification', code.substring(0, 2));
        },
        onSelect: (val: string) => {
          let options: any = [];
          //如果是小易私房钱，状态不同
          if (val === SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY) {
            options = optionsMap(privateStatusMap);
            [options[2], options[3]] = [options[3], options[2]];
          } else if (val === SECONDARY_CLASSIFICATION_CODE.MERCHANT) {
            options = optionsMap(merchantStatusMap);
          } else {
            options = optionsMap(exPrivateStatusMap);
          }
          formRef.current?.setFieldsValue({
            status: [],
          });
          setStatusOption(options);
        },
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => {
          if (value === 'HISTORY_CASH') {
            return {
              history030101Income: true,
              secondTypeCode: SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
            };
          }
          return { secondTypeCode: value };
        },
      },
    },
    {
      title: '注册时间',
      dataIndex: 'registerTime',
      valueType: 'dateRange',
      // initialValue: [defaultStartDate, currentDate],
      fieldProps: {
        ranges: defaultTimeRangeShortCut,
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.registerTime;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => {
          if (typeof value[0] !== 'string') {
            return {
              startRegisterTime: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
              endRegisterTime: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
            };
          }
          return {
            startRegisterTime: `${value[0].split(' ')[0]} 00:00:00`,
            endRegisterTime: `${value[1].split(' ')[0]} 23:59:59`,
          };
        },
      },
    },
    {
      title: 'ref值',
      dataIndex: 'ref',
      key: 'ref',
      order: -1,
      valueEnum: {
        yonghuduan: 'yonghuduan',
        yhdsy: 'yhdsy',
        yhdtc: 'yhdtc',
        yhdsyb: 'yhdsyb',
        yhdgrb: 'yhdgrb',
        sijiduan: 'sijiduan',
        sjwallet: 'sjwallet',
        qy: 'qy',
        qiyeduan: 'qiyeduan',
        qyyj: 'qyyj',
        qyyjb: 'qyyjb',
        wx_xyjk: 'wx_xyjk',
        wx_xyxd: 'wx_xyxd',
        wx_xyxd_mini: 'wx_xyxd_mini',
        xyxd: 'xyxd',
        paotui: 'paotui',
        'bjxg,huolala': 'bjxg,huolala',
        'sijichongdian-B': 'sijichongdian-B',
        'sijijiayou-B': 'sijijiayou-B',
        'yonghuchongdian-B': 'yonghuchongdian-B',
        'yonghujiayou-B': 'yonghujiayou-B',
        laladriver_personal_center: 'laladriver_personal_center',
        lalarider_homepage_box: 'lalarider_homepage_box',
        lalarider_homepage_banner: 'lalarider_homepage_banner',
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        // defaultValue:
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '申请额度',
      dataIndex: 'applyAmount',
      key: 'applyAmount',
      search: false,
    },
    {
      title: '风控进件时间',
      dataIndex: 'riskStartTime',
      valueType: 'dateRange',
      fieldProps: {
        ranges: defaultTimeRangeShortCut,
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.riskStartTime;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startRiskStartTime: `${value[0].split(' ')[0]} 00:00:00`,
          endRiskStartTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    // {
    //   title: '进件状态',
    //   dataIndex: 'incomeStatusEnum',
    //   key: 'incomeStatusEnum',
    //   // hideInTable: true,
    //   valueType: 'select',
    //   fieldProps: {
    //     options: statusOptions,
    //     showSearch: true,
    //     mode: 'multiple',
    //     showArrow: true,
    //     optionFilterProp: 'label',
    //     filterOption: (input: string, option: { label: string }) =>
    //       option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
    //   },
    // },
    {
      title: '进件状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      fieldProps: {
        options: statusOptions,
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, record) => {
        if (
          record?.history030101Income ||
          record?.secondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
        ) {
          // 历史小易速贷 或者是融租
          return exPrivateStatusMap[record.status] || record.status;
        }
        if (record?.secondTypeCode === SECONDARY_CLASSIFICATION_CODE.MERCHANT) {
          return merchantStatusMap[record.status] || record.status;
        }
        return privateStatusMap[record.status] || record.status;

        return '-';
      },
    },
    {
      title: '拒绝原因',
      dataIndex: 'rejectReason',
      key: 'rejectReason',
      search: false,
      // ellipsis: true,
      render: (rejectReason: any) => {
        // const rejectReason = '资格类资格类';
        return rejectReason?.length > 6 ? (
          <Tooltip placement="topLeft" title={rejectReason}>
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                width: 98,
                textOverflow: 'ellipsis',
              }}
            >
              {rejectReason}
            </div>
          </Tooltip>
        ) : (
          rejectReason
        );
      },
      // render: (reason) => <span>{reason}</span>,
    },
    {
      title: '风控审核时间',
      dataIndex: 'creditEndTime',
      key: 'creditEndTime',
      valueType: 'dateRange',
      fieldProps: {
        ranges: defaultTimeRangeShortCut,
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.creditEndTime ?? '-';
      },
      search: {
        transform: (value: any) => ({
          startCreditEndTime: `${value[0].split(' ')[0]} 00:00:00`,
          endCreditEndTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '授信额度',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      search: false,
    },
    {
      title: '授信利率',
      dataIndex: 'interestRate',
      key: 'interestRate',
      search: false,
      render: (_, row) => {
        // return interestRate;
        return row?.interestRate ? `${new BigNumber(row?.interestRate).times(100)}%` : '-';
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, row) => (
        <>
          <Link to={`/userMng/personalMng/detail?orderNo=${row?.orderNo}`}>查看详情</Link>
        </>
      ),
    },
  ];

  return (
    <ProTable<UserInputRecordListItem>
      columns={columns}
      actionRef={actionRef}
      formRef={formRef}
      scroll={{ x: 'max-content' }}
      rowKey="orderNo"
      search={{
        labelWidth: 120,
        defaultCollapsed: false,
      }}
      toolBarRender={() => {
        return [
          access.hasAccess('biz_download') && (
            <AsyncExport
              key="export"
              getSearchDataTotal={async () => {
                const data = await getInputRecordList(getSearchParams());
                return data?.total;
              }}
              getSearchParams={getSearchParams}
              exportAsync={incomeExport}
              taskCode={[ItaskCodeEnValueEnum.PERSONAL_ACTIVATE_MANAGER]}
            />
          ),
          <LogOff key="logoff" />,
        ];
      }}
      request={(params) => {
        // if (firstLoad && !params?.startRegisterTime) {
        //   console.log('params', params);
        //   params.startRegisterTime = `${dayjs(defaultStartDate).format('YYYY-MM-DD')} 00:00:00`;
        //   params.endRegisterTime = `${dayjs(currentDate).format('YYYY-MM-DD')} 23:59:59`;
        // }
        if (firstLoad && !params?.startCreatedAt) {
          console.log('params', params);
          params.startCreatedAt = `${dayjs(defaultStartDate).format('YYYY-MM-DD')} 00:00:00`;
          params.endCreatedAt = `${dayjs(currentDate).format('YYYY-MM-DD')} 23:59:59`;
        }
        setFirstLoad(false);
        return getInputRecordList(params).then((res) => {
          // 回显后端实际的查询注册时间的范围：1、空值，清空表单的注册时间 2、有值，回显表单注册时间
          const { queryParam = {}, ...rest } = res;
          const { startCreatedAt, endCreatedAt } = queryParam;
          // if (startRegisterTime && endRegisterTime) {
          //   // 回显表单的注册时间
          //   formRef?.current?.setFieldsValue({
          //     registerTime: [dayjs(startRegisterTime), dayjs(endRegisterTime)],
          //   });
          // } else {
          //   // 清空表单的注册时间
          //   formRef?.current?.setFieldsValue({
          //     registerTime: undefined,
          //   });
          // }
          // 创建时间
          if (startCreatedAt && endCreatedAt) {
            formRef?.current?.setFieldsValue({
              createAt: [dayjs(startCreatedAt), dayjs(endCreatedAt)],
            });
          } else {
            formRef?.current?.setFieldsValue({
              createAt: undefined,
            });
          }
          return { ...rest, data: rest.data || [] };
        });
      }}
    />
  );
};

export default RecordTable;
