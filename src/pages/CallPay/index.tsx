import HeaderTab from '@/components/HeaderTab/index';
import {
  CLASSIFICATION,
  SECONDARY_CLASSIFICATION,
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_MAP_ALL,
} from '@/enums';
import globalStyle from '@/global.less';
import { getUserListEnum } from '@/services/enum';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { isCarInsuranceStoreUser, isChannelStoreUser, isExternalNetwork } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout'; // @ts-ignore
import type { ActionType, ProColumns } from '@ant-design/pro-table'; // @ts-ignore
import ProTable from '@ant-design/pro-table';
import { history, Link, useAccess, useModel } from '@umijs/max';
import type { RadioChangeEvent } from 'antd';
import { message, Radio, Tabs } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { PassModal, RefuseModal, StatusModal } from './components';
import RepayAttach from './components/RepayAttach';
import { ACTIVE_TAB, OFFLINE_TAB } from './const';
import type { CallPayItem, CallPayItemOffLine } from './data';
import {
  callPayExport,
  getCallPayList,
  getCompensate,
  getCompensateExport,
  offLineExport,
  offlineRemitList,
} from './service';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps, removeBlankFromObject } from '@/utils/tools';

import OfflineRepayReviewList from './carInsurance/OfflineRepayReviewList'; //  还款单
import ReductionRepayReviewList from './reductionOrder/ReductionRepayReviewList'; //  减免单

const { TabPane } = Tabs;
// const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
//   return (
//     <DatePicker.RangePicker
//       {...rest}
//       className={globalStyle.w100}
//       disabledDate={(current: any) => {
//         return current && current > dayjs().endOf('day');
//       }}
//     />
//   );
// };

const CallPay: React.FC<any> = () => {
  const { tab } = history.location?.query as any;
  let DEFAULT_REPAY_KEY = OFFLINE_TAB.SINGLE_REPAYMENT; //44单期代偿，55代偿结清
  let DEFAULT_ACTIVITYTAB_KEY = tab || ACTIVE_TAB.COLLECT_COLLECT;
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [activeTabKey, setActiveTabKey] = useState(DEFAULT_ACTIVITYTAB_KEY);
  const [repayKey, setRepayKey] = useState(DEFAULT_REPAY_KEY);
  const [passModalVisible, handlePassModalVisible] = useState<boolean>(false);
  const [refuseModalVisible, handleRefuseModalVisible] = useState<boolean>(false);
  const [statusModalVisible, handleStatusModalVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<CallPayItem[]>([]);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [isCarInsurance, setIsCarInsurance] = useState(false); // 二级分类是否是车险
  const { mapUserList }: any = useModel('userList');
  const access = useAccess();
  const isChannelUser = isCarInsuranceStoreUser(access); // 是否是车险渠道用户
  const runTable = (params: any) => {
    console.log('paramsparams', params);
    // console.log(sorter);

    // 有时候  首次渲染 初始值 初始值 无法携带上
    // 会有初始值 催收员 外网情况下
    if (isExternalNetwork()) {
      if (!params?.urgePerson) {
        params.urgePerson = currentUser?.userId;
      }
    }

    console.log('activeTabKey', activeTabKey);

    if (activeTabKey === ACTIVE_TAB.COLLECT_COLLECT) {
      return getCallPayList(params).then((res) => {
        // console.log(res);
        setDataSource(res.data);
        return res;
      });
    } else {
      //55代偿结清
      if (repayKey === OFFLINE_TAB.COMPENSATE && activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT) {
        return getCompensate(params).then((res) => {
          setDataSource(res.data);
          return res;
        });
      }
      return offlineRemitList(params).then((res) => {
        setDataSource(res.data);
        return res;
      });
    }
  };
  const overdueCaseNoList = dataSource?.map((item) => {
    return item.overdueCaseNo;
  });
  const [currentRow, setCurrentRow] = useState<CallPayItem | CallPayItemOffLine>();
  const statusMap = {
    0: '待审核',
    1: '通过',
    2: '驳回',
  };

  const statusMap2 = {
    0: '审批拒绝',
    1: '待审批',
    10: '审批通过',
    2: '审核通过',
    3: '审核拒绝',
  };

  useEffect(() => {
    if (currentUser?.userId && isExternalNetwork()) {
      formRef.current?.setFieldsValue({ urgePerson: currentUser?.userId });
    }
  }, []);

  useEffect(() => {
    if (isChannelUser || isChannelStoreUser(access)) {
      DEFAULT_REPAY_KEY = OFFLINE_TAB.car_insurance_offline_repay_review;
      DEFAULT_ACTIVITYTAB_KEY = ACTIVE_TAB.OFFLINE_REPAYMENT;
      //  车险/融租渠道默认tab值
      setActiveTabKey(DEFAULT_ACTIVITYTAB_KEY);
      setRepayKey(DEFAULT_REPAY_KEY);
    }
  }, []);

  const columns: ProColumns<CallPayItem>[] = [
    {
      title: '回款单号',
      dataIndex: 'paymentId',
    },
    {
      title: '催收案件编号',
      dataIndex: 'overdueCaseNo',
      render: (_, row, dataIndex) => {
        return row?.overdueCaseNo ? (
          <Link
            to={{
              pathname: `/businessMng/postLoanMng/collection-detail`,
              search: `?overdueCaseNo=${row?.overdueCaseNo}`,
            }}
            state={{
              curList: overdueCaseNoList || [],
              curItemIndex: dataIndex,
            }}
          >
            {row.overdueCaseNo}
          </Link>
        ) : (
          '-'
        );
      },
    },
    {
      title: '申请人',
      dataIndex: 'urgePerson',
      valueType: 'select',
      request: getUserListEnum,
      fieldProps: {
        showSearch: true,
        disabled: isExternalNetwork(),
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, record) => {
        return mapUserList[record?.urgePerson];
      },
    },
    {
      title: '产品一级分类',
      dataIndex: 'secondaryName',
      search: false,
    },
    {
      title: '关联单号',
      dataIndex: 'orderNo',
      search: false,
      render: (_, row) => {
        let dom = <></>;
        //循环额度的不跳转
        if (row?.orderNo?.includes(',')) {
          dom = <>{row.orderNo}</>;
        } else {
          const strMap = {
            '03': 'cash-',
            '02': 'lease-',
            '01': '',
          };
          const isCarInsurance =
            row?.productCode?.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE;
          const carInsuranceUrl = '/businessMng/car-insurance/detail';
          dom = (
            <>
              {' '}
              {row?.orderNo && row?.orderNo !== '-' ? (
                <Link
                  to={{
                    pathname: isCarInsurance
                      ? carInsuranceUrl
                      : `/businessMng/${
                          strMap[row?.productCode?.substring(0, 2) as keyof typeof strMap]
                        }detail`,
                    search: `?orderNo=${row?.orderNo}`,
                  }}
                >
                  {row.orderNo}
                </Link>
              ) : (
                '-'
              )}
            </>
          );
        }
        return dom;
      },
    },
    {
      title: '借款人姓名',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
      search: false,
    },
    {
      title: '订单金额(元)',
      dataIndex: 'orderAmount',
      search: false,
    },
    {
      title: '逾期金额（元）',
      dataIndex: 'overdueAmount',
      search: false,
    },
    {
      title: '回款日期',
      dataIndex: 'paymentTime',
      search: false,
    },

    {
      title: '回款金额',
      dataIndex: 'amount',
      key: 'amount',
      search: false,
    },
    {
      title: '还款渠道',
      dataIndex: 'repayChannel',
      key: 'repayChannel',
      search: false,
      valueEnum: {
        0: { text: '微信二维码' },
        1: { text: '对公打款' },
      },
    },
    {
      title: '三方还款流水号',
      dataIndex: 'thirdFlowId',
      key: 'thirdFlowId',
      search: false,
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: {
        0: { text: '待审核' },
        1: {
          text: '通过',
        },
        2: {
          text: '驳回',
        },
      },
      render: (_, row) => (
        <>
          {/* <span>{dom(row)}</span> */}
          <a
            onClick={() => {
              handleStatusModalVisible(true);
              setCurrentRow(row);
            }}
          >
            {statusMap[row.status as keyof typeof statusMap]}
          </a>
        </>
      ),
    },

    {
      title: '创建时间',
      key: 'createdAt',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      initialValue: [
        dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      fieldProps: {
        allowClear: false,
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          createdAtStart: `${value[0].split(' ')[0]} 00:00:00`,
          createdAtEnd: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
      render: (_, record) => {
        return record?.createdAt;
      },
    },

    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      valueType: 'select',
      valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
      hideInTable: true,
    },
    {
      title: '渠道',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '是否代偿',
      dataIndex: 'compensation',
      valueType: 'select',
      search: false,
      valueEnum: {
        1: '是',
        0: '否',
      },
    },
    {
      title: '代偿期数',
      dataIndex: 'repayTerms',
      search: false,
    },
    {
      title: '还款凭证',
      search: false,
      dataIndex: 'attach',
      key: 'attach',
      render: (_, row) => {
        return row?.attachList ? (
          <a
            onClick={() =>
              optimizationModalWrapper(RepayAttach)({
                imgList: row?.attachList,
              })
            }
          >
            查看
          </a>
        ) : (
          <>-</>
        );
      },
    },
    {
      title: '操作',
      width: 180,
      fixed: 'right',
      valueType: 'option',
      // 权限判断展示操作列
      hideInTable: !(
        access && access.hasAccess('biz_businessMng_postLoanMng_callpay_audit_button')
      ),
      render: (_, row: CallPayItem) => (
        <>
          {/* 保理不展示操作按钮 */}
          {!row.status && row.productCode?.substr(0, 2) !== '01' && !isExternalNetwork() ? (
            <>
              <a
                className={globalStyle.mr10}
                onClick={() => {
                  handlePassModalVisible(true);
                  // confirm()
                  setCurrentRow(row);
                }}
              >
                通过
              </a>
              <a
                type="link"
                className={globalStyle.mr10}
                onClick={() => {
                  handleRefuseModalVisible(true);
                  setCurrentRow(row);
                }}
              >
                驳回
              </a>
            </>
          ) : (
            '-'
          )}
          {/* <a>查看详情</a> */}
        </>
      ),
    },
  ];

  const columns2: ProColumns<CallPayItemOffLine>[] = [
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      // initialValue: 'FINANCE_LEASE',
      valueType: 'select',
      hideInTable: true,
      valueEnum: CLASSIFICATION,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      valueType: 'select',
      valueEnum: SECONDARY_CLASSIFICATION,
      hideInTable: true,
      fieldProps: {
        onChange: (value: string) => {
          setIsCarInsurance(value === 'CAR_INSURANCE');
        },
      },
    },
    {
      title: '回款单号',
      dataIndex: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'remitApplyOrderNo' : 'compensateNo',
      key: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'remitApplyOrderNo' : 'compensateNo',
    },
    {
      title: '关联单号',
      dataIndex: 'orderNo',
      render: (_, row) => {
        let dom = <></>;
        //循环额度的不跳转
        if (row?.orderNo?.includes(',')) {
          dom = <>{row.orderNo}</>;
        } else {
          dom = (
            <a
              onClick={() => {
                if (row.productCode.slice(0, 4) === '0303') {
                  // 如果是车险的产品
                  history.push(
                    `/businessMng/postLoanMng/car-insurance/detail?orderNo=${row?.orderNo}`,
                  );
                  return;
                }
                if (!row?.productCode) {
                  message.warning('产品Code不能为空哦！');
                  return;
                }
                history.push(
                  `/businessMng/postLoanMng/after-loan-detail?orderNo=${row?.orderNo}&productCode=${row?.productCode}`,
                );
              }}
            >
              {row.orderNo}
            </a>
          );
        }
        return dom;
      },
    },
    {
      title: '申请人',
      dataIndex: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'applyName' : 'applyUserId',
      valueType: 'select',
      request: getUserListEnum,
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, record: any) => {
        let item = '';
        if (repayKey === OFFLINE_TAB.SINGLE_REPAYMENT) {
          item = mapUserList[record?.applyName as string];
        } else {
          item = record?.applyUserName;
        }
        return item;
      },
    },
    {
      title: '实际还款方',
      dataIndex: 'actualRepayRole',
      key: 'actualRepayRole',
      valueType: 'select',
      valueEnum: {
        1: '用户本人',
        2: '第三方',
      },
      search: false,
    },
    {
      title: '借款人姓名',
      dataIndex: 'accountName',
      key: 'accountName',
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
      search: false,
    },
    {
      title: '还款日期',
      dataIndex: 'repayDate',
      key: 'repayDate',
      search: false,
    },
    {
      title: '还款期数',
      dataIndex: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'termDetail' : 'repayTerms',
      key: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'termDetail' : 'repayTerms', //44 单期还款tab  55 代偿结清tab
      search: false,
      render: (_, row) => {
        let lastShow: number[] = [];
        if (row?.repayTerms?.length && Array.isArray(row?.repayTerms)) {
          // 字符串的length也存在 但没有sort方法可能报错
          // 判断 不是空数组
          lastShow = row?.repayTerms?.sort((a, b) => a - b);
        }
        return repayKey === OFFLINE_TAB.SINGLE_REPAYMENT
          ? row?.termDetail || '-'
          : `${lastShow?.[0]}~${lastShow?.[lastShow?.length - 1]}` || '-';
      },
    },
    {
      title: '应还款总金额/元',
      dataIndex: 'repayAmount',
      key: 'repayAmount',
      search: false,
    },
    {
      title: '减免总金额/元',
      dataIndex: 'remissionAmount',
      key: 'remissionAmount',
      search: false,
    },
    {
      title: '实际还款金额/元',
      dataIndex: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'remitAmount' : 'actualAmount',
      key: repayKey === OFFLINE_TAB.SINGLE_REPAYMENT ? 'remitAmount' : 'actualAmount',
      search: false,
    },
    {
      title: '还款渠道',
      dataIndex: 'repayChannel',
      key: 'repayChannel',
      search: false,
      valueEnum: {
        0: { text: '微信二维码' },
        1: { text: '对公打款' },
      },
    },
    {
      title: '三方还款流水号',
      dataIndex: 'thirdFlowId',
      key: 'thirdFlowId',
      search: false,
    },
    {
      title: '还款凭证',
      search: false,
      dataIndex: 'attach',
      key: 'attach',
      render: (_, row) => {
        return (
          <>
            <a
              onClick={() =>
                optimizationModalWrapper(RepayAttach)({
                  imgList: row?.attachList,
                })
              }
            >
              查看
            </a>
          </>
        );
      },
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: statusMap2,
      render: (_, row) => (
        <>
          {/* <span>{dom(row)}</span> */}
          <a
            onClick={() => {
              handleStatusModalVisible(true);
              setCurrentRow(row);
            }}
          >
            {statusMap2[row?.status as keyof typeof statusMap2]}
          </a>
        </>
      ),
    },
    {
      title: '创建时间',
      key: 'createdAt',
      dataIndex: 'createdAt',
      // renderFormItem,
      valueType: 'dateRange',
      initialValue: [
        dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      fieldProps: {
        allowClear: false,
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => {
          if (
            activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT &&
            repayKey === OFFLINE_TAB.COMPENSATE
          ) {
            return {
              createStartTime: dayjs(value[0]).format('YYYY-MM-DD'),
              createEndTime: dayjs(value[1]).format('YYYY-MM-DD'),
            };
          } else {
            return {
              startCreatedAt: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
              endCreatedAt: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
            };
          }
        },
      },
      render: (_, record: any) => {
        return record?.createdAt;
      },
    },
    {
      title: '车牌号',
      dataIndex: 'licensePlateNo',
      hideInTable: true,
      search: isCarInsurance ? undefined : false,
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      hideInTable: true,
      search: isCarInsurance ? undefined : false,
    },
    {
      title: '操作',
      width: 180,
      fixed: 'right',
      valueType: 'option',
      // 权限判断展示操作列
      hideInTable: !(
        access && access.hasAccess('biz_businessMng_postLoanMng_callpay_audit_button')
      ),
      render: (_, row) => (
        <>
          {/* 保理不展示操作按钮 */}
          {row?.status === 1 && row.productCode?.substr(0, 2) !== '01' ? (
            <>
              <a
                className={globalStyle.mr10}
                onClick={() => {
                  handlePassModalVisible(true);
                  // confirm()
                  setCurrentRow(row);
                }}
              >
                通过
              </a>
              <a
                type="link"
                className={globalStyle.mr10}
                onClick={() => {
                  handleRefuseModalVisible(true);
                  setCurrentRow(row);
                }}
              >
                驳回
              </a>
            </>
          ) : (
            '-'
          )}
          {/* <a>查看详情</a> */}
        </>
      ),
    },
  ];

  function handleTabChange(e: RadioChangeEvent) {
    const key = e.target.value;
    // console.log(key);
    setActiveTabKey(key);
    formRef.current?.setFieldValue('secondaryClassification', undefined); // 并不能清空 request值
    if (key !== activeTabKey && key !== ACTIVE_TAB.EARLY_SETTLEMENT) {
      setRepayKey(DEFAULT_REPAY_KEY);
      setTimeout(() => {
        formRef.current?.submit();
      });
    } else if (key === ACTIVE_TAB.EARLY_SETTLEMENT) {
      history.push('/businessMng/postLoanMng/early-end-pay');
    }
  }

  function handleRepayChange(val: string) {
    setRepayKey(val);
    if (activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT) {
      setTimeout(() => {
        formRef.current?.submit();
      });
    }
  }

  function getSearchParams() {
    const { createdAt, ...data } = formRef?.current?.getFieldsValue();
    let newForm = { ...data };
    if (createdAt?.length) {
      const createdAtStart = `${dayjs(createdAt[0]).format('YYYY-MM-DD')} 00:00:00`;
      const startCreatedAt = `${dayjs(createdAt[0]).format('YYYY-MM-DD')} 00:00:00`;
      const createStartTime = `${dayjs(createdAt[0]).format('YYYY-MM-DD')}`;
      const createdAtEnd = `${dayjs(createdAt[1]).format('YYYY-MM-DD')} 23:59:59`;
      const endCreatedAt = `${dayjs(createdAt[1]).format('YYYY-MM-DD')} 23:59:59`;
      const createEndTime = `${dayjs(createdAt[1]).format('YYYY-MM-DD')}`;
      const objTime =
        activeTabKey === ACTIVE_TAB.COLLECT_COLLECT
          ? { createdAtStart, createdAtEnd }
          : activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT && repayKey === OFFLINE_TAB.COMPENSATE
          ? { createStartTime, createEndTime }
          : { startCreatedAt, endCreatedAt };
      newForm = {
        ...data,
        ...objTime,
        current: 1,
        pageSize: 20,
      };
    }
    return removeBlankFromObject(filterProps(newForm));
  }

  function renderTab() {
    return (
      <>
        {access.hasAccess('biz_businessMng_postLoanMng_callpay_overdueRefund') && (
          <Radio.Button value={ACTIVE_TAB.COLLECT_COLLECT}>催收回款</Radio.Button>
        )}
        {access.hasAccess('biz_businessMng_postLoanMng_callpay_offlineRefund') && (
          <Radio.Button value={ACTIVE_TAB.OFFLINE_REPAYMENT}>还款审核</Radio.Button>
        )}
        {access.hasAccess('biz_businessMng_postLoanMng_callpay_earlySettlement') && (
          <Radio.Button value={ACTIVE_TAB.EARLY_SETTLEMENT}>提前结清</Radio.Button>
        )}
      </>
    );
    // const a = <Radio.Button value={ACTIVE_TAB.COLLECT_COLLECT}>催收回款</Radio.Button>;
    // const b = <Radio.Button value={ACTIVE_TAB.OFFLINE_REPAYMENT}>还款审核</Radio.Button>;
    // const c = <Radio.Button value={ACTIVE_TAB.EARLY_SETTLEMENT}>提前结清</Radio.Button>;
    // if (isExternalNetwork()) {
    //   if (isChannelUser) {
    //     return b;
    //   } else {
    //     return a;
    //   }
    // } else {
    //   if (isChannelUser) {
    //     return b;
    //   } else {
    //     return (
    //       <>
    //         {a}
    //         {b}
    //         {c}
    //       </>
    //     );
    //   }
    // }
  }

  function renderHeaderTitleJsx() {
    return (
      <div>
        <Radio.Group
          // defaultValue="1"
          onChange={handleTabChange}
          buttonStyle="solid"
          optionType="button"
          value={activeTabKey}
        >
          {renderTab()}
        </Radio.Group>

        {activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT && ( //线下回款分为单期还款 和代偿结清
          <Tabs
            style={{ marginTop: 8, marginLeft: 5 }}
            defaultActiveKey={OFFLINE_TAB.SINGLE_REPAYMENT}
            onChange={handleRepayChange}
            activeKey={repayKey}
          >
            {access.hasAccess('biz_businessMng_postLoanMng_callpay_offlineRefund_singlePhase') && (
              <TabPane tab="单期还款" key={OFFLINE_TAB.SINGLE_REPAYMENT} />
            )}
            {access.hasAccess('biz_businessMng_postLoanMng_callpay_offlineRefund_compensatory') && (
              <TabPane tab="代偿结清" key={OFFLINE_TAB.COMPENSATE} />
            )}
            {access.hasAccess('bill_apply_list_postLoanMng_callpay') && (
              <TabPane tab="还款单" key={OFFLINE_TAB.car_insurance_offline_repay_review} />
            )}
            {access.hasAccess('bill_apply_remission_info_postLoanMng_callpay') && (
              <TabPane tab="减免单" key={OFFLINE_TAB.reduction_repay_review} />
            )}
          </Tabs>
        )}
      </div>
    );
  }

  function RenderAsyncExport() {
    return (
      <AsyncExport
        key={`${activeTabKey}_${repayKey || 0}_export`}
        getSearchDataTotal={async () => {
          if (activeTabKey === ACTIVE_TAB.COLLECT_COLLECT) {
            return getCallPayList(getSearchParams()).then((res) => res?.total);
          } else {
            if (
              repayKey === OFFLINE_TAB.COMPENSATE &&
              activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT
            ) {
              return getCompensate(getSearchParams()).then((res) => res?.total);
            }
            return offlineRemitList(getSearchParams()).then((res) => res?.total);
          }
        }}
        getSearchParams={getSearchParams}
        exportAsync={(params) => {
          if (activeTabKey === ACTIVE_TAB.COLLECT_COLLECT) {
            return callPayExport(params);
          } else {
            //如果是线下回款 多期代偿
            if (
              activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT &&
              repayKey === OFFLINE_TAB.COMPENSATE
            ) {
              return getCompensateExport(params);
            } else {
              return offLineExport(params);
            }
          }
        }}
        taskCode={[
          activeTabKey === ACTIVE_TAB.COLLECT_COLLECT
            ? ItaskCodeEnValueEnum.CALL_PAYMENT_OVERDUE_REFUND
            : repayKey === OFFLINE_TAB.SINGLE_REPAYMENT
            ? ItaskCodeEnValueEnum.CALL_PAYMENT_OFFLINE_REFUND_SINGLE_PERIOD
            : ItaskCodeEnValueEnum.CALL_PAYMENT_OFFLINE_REFUND_COMPENSATORY,
        ]}
      />
    );
  }

  return (
    <>
      <HeaderTab />
      <PageContainer>
        {repayKey === OFFLINE_TAB.car_insurance_offline_repay_review ? (
          <OfflineRepayReviewList renderHeaderTitleJsx={renderHeaderTitleJsx} />
        ) : repayKey === OFFLINE_TAB.reduction_repay_review ? (
          <ReductionRepayReviewList renderHeaderTitleJsx={renderHeaderTitleJsx} />
        ) : (
          <ProTable<CallPayItem>
            columns={(activeTabKey === ACTIVE_TAB.COLLECT_COLLECT ? columns : columns2) as any}
            actionRef={actionRef}
            formRef={formRef}
            scroll={{ x: 'max-content' }}
            rowKey="paymentId"
            search={{
              labelWidth: 100,
            }}
            headerTitle={renderHeaderTitleJsx()}
            toolBarRender={() => [!isExternalNetwork() && <RenderAsyncExport />]}
            request={(params) => runTable(params)}
          />
        )}

        {/* 催收通过弹窗 */}
        <PassModal
          onOk={async () => {
            handlePassModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          onCancel={() => {
            handlePassModalVisible(false);
            setCurrentRow(undefined);
          }}
          repayKey={repayKey}
          title={activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT ? '还款审核' : '催收回款'}
          passModalVisible={passModalVisible}
          values={currentRow?.paymentId || ''}
          keyTitle={activeTabKey}
          offlineRemitOrderNo={currentRow?.remitApplyOrderNo}
          compensateNo={currentRow?.compensateNo}
        />

        {/* 催收驳回 */}
        <RefuseModal
          onOk={async () => {
            handleRefuseModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          repayKey={repayKey}
          title={activeTabKey === ACTIVE_TAB.OFFLINE_REPAYMENT ? '还款审核' : '催收回款'}
          onCancel={() => {
            handleRefuseModalVisible(false);
            setCurrentRow(undefined);
          }}
          onVisibleChange={handleRefuseModalVisible}
          refuseModalVisible={refuseModalVisible}
          values={currentRow?.paymentId || ''}
          keyTitle={activeTabKey}
          offlineRemitOrderNo={currentRow?.remitApplyOrderNo}
          compensateNo={currentRow?.compensateNo}
        />
        {/* <obj.PassModal/> */}
        <StatusModal
          onOk={async () => {
            handleStatusModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          onCancel={() => {
            handleStatusModalVisible(false);
            setCurrentRow(undefined);
          }}
          statusModalVisible={statusModalVisible}
          values={currentRow || {}}
        />
      </PageContainer>
    </>
  );
};

export default CallPay;
