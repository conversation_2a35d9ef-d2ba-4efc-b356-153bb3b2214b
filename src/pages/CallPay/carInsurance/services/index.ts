import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import {
  IapplyOrderExportParams,
  IapplyOrderInfo,
  IofflineRepayReviewItem,
  IofflineRepayReviewListParams,
  IsubmitBillAudit,
} from '../types';

// 车险线下还款审核列表
export async function offlineRepayReviewList(
  params: IofflineRepayReviewListParams,
): Promise<{ total: number; data: IofflineRepayReviewItem[] }> {
  return request('/bizadmin/bill/applyOrderList', {
    method: 'post',
    data: params,
    headers,
    ifTrimParams: true,
  });
}

// 还款审核 线下还款详情
export async function applyOrderInfo(params: { businessNo: string }): Promise<IapplyOrderInfo> {
  return request('/bizadmin/bill/applyOrderInfo', {
    method: 'post',
    data: params,
    headers,
  });
}

// 提交还款审核 审核通过 驳回
export async function submitBillAudit(params: IsubmitBillAudit) {
  return request('/bizadmin/bill/submitBillAudit', {
    method: 'post',
    data: params,
    headers,
  });
}

// 车险线下还款审核列表 - 异步导出
export async function applyOrderExport(params: IapplyOrderExportParams) {
  return request('/bizadmin/bill/applyOrderExport', {
    method: 'post',
    data: params,
    headers,
    ifTrimParams: true,
  });
}

// 还款单 取消
export async function repayOrderApplyCancel(params: IsubmitBillAudit) {
  return request('/bizadmin/bill/repayOrderApplyCancel', {
    method: 'post',
    data: params,
    headers,
  });
}

// 获取融租渠道专属账号信息
export async function getChannelAccountInfo(params: { channelCode: string }) {
  return request(`/bizadmin/channel/detail`, {
    method: 'GET',
    params,
    headers,
    skipGlobalErrorTip: true,
  });
}
