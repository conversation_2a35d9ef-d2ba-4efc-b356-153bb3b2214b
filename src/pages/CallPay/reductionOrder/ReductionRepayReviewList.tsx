/**
 * 回款审核-线下回款-减免单
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { RePaymentQrViewer } from '@/components/QrViewer/RepaymentQrViewer';
import {
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE_REMISSION,
  SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL,
} from '@/enums';
import { LEVEL } from '@/pages/CarInsurance/type';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { isCarInsuranceStoreUser, isChannelStoreUser } from '@/utils/utils'; // @ts-ignore
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components'; // @ts-ignore
import { ProTable } from '@ant-design/pro-components';
import { useAccess, useModel } from '@umijs/max';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect, useRef, useState } from 'react';
import OnlineRepayReviewModal from '../carInsurance/components/OnlineRepayReviewModal';
import { repayModeMap } from '../carInsurance/types';
import { PAY_TYPE } from '../const';
import { useChannelListSummarizeSer } from '../service';
import RemissionReviewModal from './components/RemissionReviewModal';
import { applyOrderRemissionExport, applyOrderRemissionList } from './services';
import styles from './styles/index.less';
import { IofflineRepayReviewItem, remissionStatusMap } from './types';

type Props = {
  renderHeaderTitleJsx: () => any;
};
const ReductionRepayReviewList: React.FC<Props> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { renderHeaderTitleJsx } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [record, setRecord] = useState<IofflineRepayReviewItem>();
  const actionRef = useRef<ActionType>();
  const access = useAccess();
  const { initialState = {} }: any = useModel('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;
  const [secondaryClassification, setSecondaryClassification] = useState('');
  const channelListSummarize = useChannelListSummarizeSer({
    secondaryClassification,
    channelCode,
    channelLevel,
  });
  const [remissionReviewOpen, setRemissionReviewOpen] = useState(false); //  减免单详情
  const [onlineRepayReviewOpen, setOnlineRepayReviewOpen] = useState(false); //  还款单详情
  const [qrVisible, setQrVisible] = useState(false);
  const [qrData, setQrData] = useState({});

  useEffect(() => {
    setSecondaryClassification(isCarInsuranceStoreUser(access) ? 'CAR_INSURANCE' : 'FINANCE_LEASE'); //  默认小圆车融
  }, [currentUser]);

  const OfflineRepayReviewListColumns: ProColumns<IofflineRepayReviewItem>[] = [
    {
      title: '还款单ID',
      dataIndex: 'businessNo',
      order: 1,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondProductCode',
      valueType: 'select',
      valueEnum: SECONDARY_CLASSIFICATION_CODE_REMISSION,
      initialValue: isCarInsuranceStoreUser(access)
        ? SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE
        : SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
      order: 2,
      fieldProps: {
        onChange: (val) => {
          formRef?.current?.setFieldsValue({
            //  清空一下联动
            channelCode: '',
            repayMode: '',
          });
          setSecondaryClassification(SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL[val as any] || '');
        },
        allowClear: false,
        disabled: isChannelStoreUser(access) || isCarInsuranceStoreUser(access),
      },
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.orderNoList.map((orderNo) => {
                  return <div>{orderNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.orderNo}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '车架号',
      dataIndex: 'subjectUniqueNo',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.subjectUniqueNoList.map((subjectUniqueNo) => {
                  return <div>{subjectUniqueNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.subjectUniqueNo}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '还款场景',
      dataIndex: 'repayMode',
      valueType: 'select',
      valueEnum: repayModeMap[secondaryClassification],
      render(_, record) {
        return <>{record?.repayModeName || '-'}</>;
        const valueEnum =
          repayModeMap[SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL[record?.secondProductCode]] ||
          {};
        return <>{valueEnum[record?.repayMode] || '-'}</>;
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channelCode',
      valueType: 'select',
      hideInTable: true,
      params: { key: secondaryClassification },
      request: async () => channelListSummarize,
      //二级车险渠道只有自己，禁用渠道同时，选中自己
      initialValue:
        (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL) ||
        (isChannelStoreUser(access) && !!channelCode)
          ? channelCode
          : undefined,
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        disabled:
          (isChannelStoreUser(access) && !!channelCode) ||
          (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL),
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '客户名称',
      dataIndex: 'accountName',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.accountNameList.map((accountName) => {
                  return <div>{accountName}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.accountName}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '应还款总额(元)',
      dataIndex: 'planRepayAmount',
      search: false,
    },
    {
      title: '还款总额(元)',
      dataIndex: 'repayAmount',
      search: false,
    },
    {
      title: '减免总额(元)',
      dataIndex: 'remissionAmountTotal',
      search: false,
    },
    {
      title: '支付方式',
      dataIndex: 'remitType',
      search: true,
      valueEnum: PAY_TYPE,
      render(_, record) {
        return PAY_TYPE?.[record?.remitType as keyof typeof PAY_TYPE] || '-';
      },
    },
    {
      title: '减免本金(元)',
      dataIndex: 'remissionAmountPrincipal',
      search: false,
    },
    {
      title: '减免利息(元)',
      dataIndex: 'remissionAmountInterest',
      search: false,
    },
    {
      title: '减免罚息(元)',
      dataIndex: 'remissionAmountOverdueAmount',
      search: false,
    },
    {
      title: '减免违约金(元)',
      dataIndex: 'remissionAmountLiquidateDamages',
      search: false,
    },
    {
      title: '还款日期',
      dataIndex: 'repayDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(1, 'year'), dayjs()],
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            repayDateStart: `${value[0]} 00:00:00`,
            repayDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.repayDate || '-';
      },
    },
    {
      title: '入账日期',
      dataIndex: 'creditDate',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            creditDateStart: `${value[0]} 00:00:00`,
            creditDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.creditDate || '-';
      },
    },
    {
      title: '减免单状态',
      dataIndex: 'remissionApprovalStatus',
      valueType: 'select',
      valueEnum: remissionStatusMap,
    },

    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => {
        // 权限
        const hasAceess = access.hasAccess('bill_apply_info_postLoanMng_callpay');
        // 融租
        const showLeaseDetail =
          record?.secondProductCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND;
        // 车险
        const showCarInsuranceDetail =
          record?.secondProductCode === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE &&
          record?.remitType === 4;
        //
        if (hasAceess && (showLeaseDetail || showCarInsuranceDetail)) {
          return (
            <>
              <div
                onClick={() => {
                  setRemissionReviewOpen(true);
                  setRecord(record);
                }}
              >
                <a>查看详情</a>
              </div>
            </>
          );
        }
        return '-';
      },
    },
  ];

  return (
    <>
      <ProTable
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        columns={OfflineRepayReviewListColumns}
        request={async (params) => {
          const { current = 1, pageSize = 20 } = params;
          return applyOrderRemissionList(
            removeBlankFromObject(filterProps({ ...params, current, pageSize })),
          );
        }}
        scroll={{ x: 'max-content' }}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (_selectedRowKeys) => {
            setSelectedRowKeys(_selectedRowKeys);
          },
        }}
        onReset={() => {
          setSecondaryClassification(
            isCarInsuranceStoreUser(access) ? 'CAR_INSURANCE' : 'FINANCE_LEASE',
          ); //  默认小圆车融
        }}
        rowKey="businessNo"
        className={styles['offline-repay-review-list']}
        headerTitle={renderHeaderTitleJsx()}
        formRef={formRef}
        actionRef={actionRef}
        toolBarRender={() => {
          return [
            access.hasAccess('bill_order_apply_export_postLoanMng_callpay') && (
              <AsyncExport
                getSearchDataTotal={async () => {
                  const searchParams = formRef.current?.getFieldsFormatValue?.();
                  const data = await applyOrderRemissionList(
                    removeBlankFromObject(
                      filterProps({
                        ...searchParams,
                        current: 1,
                        pageSize: 1,
                      }),
                    ),
                  );
                  return data?.total;
                }}
                getSearchParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return removeBlankFromObject(filterProps({ ...params }));
                }}
                getSelectedParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return {
                    businessNoList: selectedRowKeys,
                    channelCode: params?.channelCode,
                  };
                }}
                getSelectedTotal={() => {
                  return selectedRowKeys.length;
                }}
                exportAsync={applyOrderRemissionExport}
                taskCode={[ItaskCodeEnValueEnum.REPAY_APPLY_REMISSION_APPROVE]}
              />
            ),
          ];
        }}
      />
      <RemissionReviewModal
        record={record}
        remissionReviewOpen={remissionReviewOpen}
        setRemissionReviewOpen={setRemissionReviewOpen}
        setOnlineRepayReviewOpen={setOnlineRepayReviewOpen}
        actionRef={actionRef}
      />
      <OnlineRepayReviewModal
        record={record}
        onlineRepayReviewOpen={onlineRepayReviewOpen}
        setOnlineRepayReviewOpen={setOnlineRepayReviewOpen}
        setRemissionReviewOpen={setRemissionReviewOpen}
        actionRef={actionRef}
        setQrData={setQrData}
        setQrVisible={setQrVisible}
      />
      <RePaymentQrViewer
        data={qrData}
        qrVisible={qrVisible}
        handleQRVisible={setQrVisible}
        title="还款二维码"
      />
    </>
  );
};

export default memo(ReductionRepayReviewList);
