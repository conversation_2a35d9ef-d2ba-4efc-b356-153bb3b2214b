import { CHANNEL_TYPES_MAP, ENERGY_TYPE_MAP, GUARANTEE_TYPES } from '@/enums';
import globalStyle from '@/global.less';
import { getChannel } from '@/pages/Channel/service';
import { getProductNameEnum } from '@/services/enum';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { Popover } from 'antd';
import BigNumber from 'bignumber.js';
import React from 'react';
import RegionInputInterest from '../Product/components/RegionInputInterest';
import { CAR_STATUS, CAR_TYPE, CHANNEL_STATUS, STATIC_CAR_STATUS } from './consts';
import { findQuery } from './service';
import type {
  TCarInfoTableItem,
  TChannelTableItem,
  TProductTableItem,
  TStaticCarInfoTableItem,
  TStaticChannelTableItem,
} from './type';
/**
 *  产品信息表格
 */
export const productColumns: ProColumns<TProductTableItem>[] = [
  {
    title: '产品编码',
    dataIndex: 'businessKey',
    // valueType: 'select',
    // request: () => productCodeEnum('02'),
    // fieldProps: {
    //   showSearch: true,
    //   showArrow: true,
    //   optionFilterProp: 'label',
    //   filterOption: (input: string, option: { label: string }) =>
    //     option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
    // },
    render: (_, row) => {
      return (
        <Link to={`/businessMng/product-lease-detail?productCode=${row?.businessKey}`}>
          {row?.businessKey}
        </Link>
      );
    },
  },
  {
    title: '产品名称',
    dataIndex: 'businessKey',
    valueType: 'select',
    request: () => getProductNameEnum('02'),
    hideInTable: true,
    fieldProps: {
      showSearch: true,
      // mode: 'multiple',
      showArrow: true,
      optionFilterProp: 'label',
      filterOption: (input: string, option: { label: string }) =>
        option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
    },
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    search: false,
    width: 150,
  },
  {
    title: '利率',
    dataIndex: 'interest',
    colSize: 24,
    renderFormItem: (_: any, { type, defaultRender, ...rest }: any) => {
      return (
        <RegionInputInterest
          minValueReplace="startInterest"
          maxValueReplace="endInterest"
          {...rest}
          className={globalStyle.w100}
        />
      );
    },
    render: (_, row) => {
      return row?.interest ? (
        <span>{new BigNumber(row.interest).times(100).toString()}%</span>
      ) : (
        <>-</>
      );
    },
  },
  {
    title: '期数',
    dataIndex: 'periods',
    key: 'periods',
    search: false,
    render: (_, row) => {
      return row?.periodsList ? row?.periodsList?.join('/') : '-';
    },
  },
  {
    title: '是否担保',
    dataIndex: 'guaranteeType',
    valueType: 'select',
    valueEnum: GUARANTEE_TYPES,
  },
  {
    title: '担保期数',
    dataIndex: 'guaranteePeriods',
    valueType: 'digit',
    key: 'guaranteePeriods',
  },
  {
    title: '状态',
    dataIndex: 'status',
    initialValue: '1',
    valueType: 'select',
    valueEnum: {
      0: { text: '禁用', status: 'Error' },
      1: { text: '启用', status: 'Success' },
    },
  },
];

/**
 *  渠道信息表格
 */
export const getChannelColumns = () => {
  const list: ProColumns<TChannelTableItem>[] = [
    {
      title: '渠道编码',
      dataIndex: 'businessKey',
      search: false,
    },
    {
      title: '渠道编码',
      dataIndex: 'channelBusinessKeyList',
      hideInTable: true,
      request: async () => {
        const data: any = await getChannel();
        return data.map((item: any) => {
          return {
            value: item.id,
            label: item.id,
          };
        });
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        dropdownMatchSelectWidth: false,
      },
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      valueEnum: CHANNEL_TYPES_MAP,
      search: false,
      renderText: (val: number) => {
        return CHANNEL_TYPES_MAP[val] || '-';
      },
    },
    {
      title: '渠道类型',
      dataIndex: 'channelTypeList',
      valueEnum: CHANNEL_TYPES_MAP,
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'name',
      width: 150,
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelNameList',
      valueType: 'select',
      hideInTable: true,
      request: async () => {
        const data = await getChannel();
        const names = [
          ...new Set(
            data.map((item: any) => {
              return item.channelName;
            }),
          ),
        ];
        return names.map((item) => {
          return {
            value: item,
            label: item,
          };
        });
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        dropdownMatchSelectWidth: false,
      },
    },
    {
      title: '销售城市',
      dataIndex: 'saleCityString',
      width: 150,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: CHANNEL_STATUS,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'channelStatusList',
      valueEnum: CHANNEL_STATUS,
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
    },
  ];
  return list;
};

/**
 *  查询视图-渠道信息表格
 */
export const staticChannelColumns: ProColumns<TStaticChannelTableItem>[] = [
  {
    title: '渠道编码',
    dataIndex: 'id',
    search: false,
  },
  {
    title: '渠道编码',
    dataIndex: 'channelBusinessKeyList',
    hideInTable: true,
    request: async () => {
      const data: any = await getChannel();
      return data.map((item: any) => {
        return {
          value: item.id,
          label: item.id,
        };
      });
    },
    fieldProps: {
      showSearch: true,
      mode: 'multiple',
      dropdownMatchSelectWidth: false,
    },
  },
  {
    title: '渠道类型',
    dataIndex: 'channelType',
    valueType: 'select',
    valueEnum: CHANNEL_TYPES_MAP,
    search: false,
  },
  {
    title: '渠道类型',
    dataIndex: 'channelTypeList',
    valueType: 'select',
    valueEnum: CHANNEL_TYPES_MAP,
    hideInTable: true,
    fieldProps: {
      mode: 'multiple',
    },
  },
  {
    title: '渠道名称',
    dataIndex: 'channelName',
    width: 150,
    search: false,
  },
  {
    title: '渠道名称',
    dataIndex: 'channelNameList',
    valueType: 'select',
    hideInTable: true,
    request: async () => {
      const data = await getChannel();
      const names = [
        ...new Set(
          data.map((item: any) => {
            return item.channelName;
          }),
        ),
      ];
      return names.map((item) => {
        return {
          value: item,
          label: item,
        };
      });
    },
    fieldProps: {
      showSearch: true,
      mode: 'multiple',
      dropdownMatchSelectWidth: false,
    },
  },
  {
    title: '销售城市',
    dataIndex: 'saleCityString',
    width: 150,
    search: false,
  },
  {
    title: '状态',
    dataIndex: 'status',
    valueEnum: CHANNEL_STATUS,
    search: false,
  },
  {
    title: '状态',
    dataIndex: 'channelStatusList',
    valueEnum: CHANNEL_STATUS,
    hideInTable: true,
    fieldProps: {
      mode: 'multiple',
    },
  },
];

/**
 *  车辆信息表格
 */
export const getCarInfoColumns = () => {
  const list: ProColumns<TCarInfoTableItem>[] = [
    {
      title: '车辆编码',
      dataIndex: 'businessKey',
      key: 'carKey',
      search: false,
      render: (_, row) => {
        return (
          <Link
            to={`/sysMng/dataDiction/carModelLibrary?carNo=${row?.businessKey}&carType=${row?.carType}`}
          >
            {row?.businessKey}
          </Link>
        );
      },
    },
    {
      title: '车辆编码',
      dataIndex: 'carBusinessKeyList',
      valueType: 'select',
      hideInTable: true,
      request: () => findQuery({ key: 'bussinessKey' }),
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '车辆类型',
      dataIndex: 'carType',
      valueEnum: CAR_TYPE,
      search: false,
    },
    {
      title: '车辆类型',
      dataIndex: 'carTypeList',
      valueEnum: CAR_TYPE,
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '车辆名称',
      dataIndex: 'name',
      key: 'name',
      search: false,
      render: (_, row) => {
        return row?.name || '-';
      },
    },
    {
      title: '车辆名称',
      dataIndex: 'carNameList',
      key: 'carNameList',
      valueType: 'select',
      hideInTable: true,
      request: () => findQuery({ key: 'carName' }),
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '车型码',
      dataIndex: 'carModelCode',
      search: false,
    },
    {
      title: '车型码',
      dataIndex: 'carModelCodeList',
      key: 'carModelCodeList',
      valueType: 'select',
      hideInTable: true,
      request: () => findQuery({ key: 'carModelCode' }),
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '能源类型',
      dataIndex: 'energyType',
      valueEnum: ENERGY_TYPE_MAP,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '全包价',
      dataIndex: 'totalPrice',
      search: false,
    },
    {
      title: '库存城市',
      dataIndex: 'carCityList',
      renderText: (val) => {
        if (Array.isArray(val) && val.length > 0) {
          return val.join(',');
        }
        return '-';
      },
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: CAR_STATUS,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'carStatusList',
      valueEnum: CAR_STATUS,
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
    },
  ];
  return list;
};

// 查看视图-车辆表格列
export const getStaticCarInfoColumns = () => {
  const list: ProColumns<TStaticCarInfoTableItem>[] = [
    {
      title: '车辆编号',
      dataIndex: 'carNo',
      search: false,
    },
    {
      title: '车辆编码',
      dataIndex: 'carBusinessKeyList',
      valueType: 'select',
      request: () => findQuery({ key: 'bussinessKey' }),
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '车辆类型',
      dataIndex: 'carType',
      valueEnum: CAR_TYPE,
      search: false,
    },
    {
      title: '车辆类型',
      dataIndex: 'carTypeList',
      valueEnum: CAR_TYPE,
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '车辆名称',
      dataIndex: 'carModel',
      width: 200,
      search: false,
    },
    {
      title: '车辆名称',
      dataIndex: 'carNameList',
      key: 'carNameList',
      valueType: 'select',
      hideInTable: true,
      request: () => findQuery({ key: 'carName' }),
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '车型码',
      dataIndex: 'carModelCode',
      search: false,
    },
    {
      title: '车型码',
      dataIndex: 'carModelCodeList',
      key: 'carModelCodeList',
      valueType: 'select',
      hideInTable: true,
      request: () => findQuery({ key: 'carModelCode' }),
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '能源类型',
      dataIndex: 'energyType',
      valueEnum: ENERGY_TYPE_MAP,
      search: false,
    },
    {
      title: '能源类型',
      dataIndex: 'energyTypeList',
      valueEnum: ENERGY_TYPE_MAP,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '全包价',
      dataIndex: 'totalPrice',
      search: false,
    },
    {
      title: '库存城市',
      dataIndex: 'carCityList',
      search: false,
      render: (_, record) => {
        if (!record.carCityDetailList || record.carCityDetailList?.length === 0) {
          return '-';
        }
        const popoverContent = record.carCityDetailList.map((value, index) => {
          return <div key={index}>{value}</div>;
        });
        const content = (
          <span>
            {record.carCityList}
            <Popover content={popoverContent} trigger="hover">
              <ExclamationCircleOutlined style={{ color: 'red', marginLeft: '4px' }} />
            </Popover>
          </span>
        );
        return content;
      },
    },
    {
      title: '状态',
      dataIndex: 'statusFlag',
      valueEnum: STATIC_CAR_STATUS,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'carStatusList',
      valueEnum: STATIC_CAR_STATUS,
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
    },
  ];
  return list;
};
