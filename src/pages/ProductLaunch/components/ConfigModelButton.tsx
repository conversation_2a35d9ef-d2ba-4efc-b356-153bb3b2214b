import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import type { TSaveOrDelConfigInfoParams, TTransferItem } from '../type';

export type TConfigModelButtonProps = {
  buttonFileProps?: Record<string, string | boolean | never>; // 按钮的原生参数
  configModelButtonParams: {
    // 组件本身的参数
    buttonText?: string; // 按钮文字, // 默认为“配置”
    titleDes?: React.ReactNode; //  弹窗标题描述
    children: any;
    getHasConfigList: () => Promise<Record<string, never>>; // 获取已经配置的列表数据
    getNotConfigList: () => Promise<Record<string, never>>; // 获取未配置的列表数据
    saveOrDelConfigInfo: (data: TSaveOrDelConfigInfoParams) => Promise<Record<string, never>>; // 保存配置的数据
    refresh: () => void; // 刷新列表数据
    disabledOptions?: { key: string; val: string }; //  是否开启列表禁用选项
  };
};

const ConfigModelButton: React.FC<TConfigModelButtonProps> = (props) => {
  const { buttonFileProps, configModelButtonParams } = props;
  const {
    buttonText,
    titleDes,
    children: TableTransfer,
    getHasConfigList,
    getNotConfigList,
    saveOrDelConfigInfo,
    refresh,
    disabledOptions,
  } = configModelButtonParams;

  const ref = useRef({ targetKeys: [], type: '' });

  /**
   * 保存未配置的数据列表
   */
  const [notConfigList, setNotConfigList] = useState<TTransferItem[]>([]);
  /**
   * 获取已配置的数据列表
   */
  const [hasConfigList, setHasConfigList] = useState<TTransferItem[]>([]);
  /**
   * 弹窗的确认 loading
   */
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  /**
   * 控制弹窗的显隐
   */
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

  /**
   * 打开弹窗
   */
  const showModal = () => {
    /**
     * 获取 配置 和 未配置 的数据
     */
    Promise.all([getHasConfigList(), getNotConfigList()]).then((res) => {
      setHasConfigList(res[0].data || []);
      setNotConfigList(res[1].data || []);
      setIsModalVisible(true);
    });
  };
  /**
   * 关闭弹窗
   */
  const handleCancel = () => {
    setIsModalVisible(false);
  };
  /**
   * 保存配置信息
   */
  const handleSaveConfigInfo = async () => {
    /**
     * 用户操作之后，新的配置数据
     */
    const { targetKeys: newTargetKeys, type }: { targetKeys: string[]; type: string } = ref.current;
    /**
     * 找出 增加 的配置数据
     */
    const addTargetList: { code: string; name: string }[] = [];
    const hasConfigKey = hasConfigList.map((item) => item.businessKey);
    newTargetKeys
      .filter((item1) => !hasConfigKey.includes(item1))
      .forEach((item2) => {
        notConfigList.forEach((item3) => {
          if (item3.businessKey === item2) {
            addTargetList.push({
              code: item3.businessKey,
              name: item3.name || '',
            });
          }
        });
      });
    /**
     * 找出 删除 的配置数据
     */
    const delTargetIds = hasConfigList
      .filter((item1) => !newTargetKeys.includes(item1.businessKey))
      .map((item2) => item2.id);
    const delBusinessKeys = hasConfigList
      .filter((item1) => !newTargetKeys.includes(item1.businessKey))
      .map((item2) => item2.businessKey);
    setBtnLoading(true);
    try {
      const params =
        type === 'city'
          ? { addList: addTargetList, deleteBusinessKeys: delBusinessKeys }
          : { addList: addTargetList, deleteIds: delTargetIds };
      await saveOrDelConfigInfo(params);
      setBtnLoading(false);
      refresh();
      handleCancel();
      message.success('配置成功！');
    } catch {
      setBtnLoading(false);
    }
  };

  return (
    <>
      <Button
        key="configButton"
        {...buttonFileProps}
        onClick={() => {
          showModal();
        }}
        style={{ paddingLeft: '10px', paddingRight: '10px' }}
      >
        {buttonText || '配置'}
      </Button>
      <Modal
        destroyOnClose
        title={buttonText}
        width={1200}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={handleSaveConfigInfo}
        confirmLoading={btnLoading}
      >
        {titleDes}
        <TableTransfer
          ref={ref}
          notConfigData={notConfigList}
          hasConfigData={hasConfigList}
          disabledOptions={disabledOptions}
        />
      </Modal>
    </>
  );
};

export default ConfigModelButton;
