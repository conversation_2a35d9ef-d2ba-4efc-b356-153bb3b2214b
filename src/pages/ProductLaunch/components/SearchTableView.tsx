// 渠道-产品-车辆联动查询视图
import { ProCard, ProTable } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table';
import { But<PERSON>, Col, Row } from 'antd';
import BigNumber from 'bignumber.js';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { getStaticCarInfoColumns, productColumns, staticChannelColumns } from '../columns';
import { enToCnMaps, originMaps, STATUS } from '../consts';
import styles from '../index.less';
import { getProductByPage, linkageQuery, queryCarInfoByPage, queryChannel } from '../service';

const defaultData: any = {
  productLaunchChannelList: [],
  productLaunchCarList: [],
  productLaunchList: [],
};

type SearchTableViewProps = {
  show: boolean;
};

const searchOptions: any = { span: 12, layout: 'horizontal', defaultCollapsed: false };

const SearchTableView: React.FC<SearchTableViewProps> = (props) => {
  const { show } = props;

  const ref = {
    CHANNEL: useRef<ActionType>(), // 渠道ref
    PRODUCT: useRef<ActionType>(), // 产品ref
    CAR: useRef<ActionType>(), // 车型ref
  };

  const selectData: any = useRef({
    // 提交数据记录对象
    ...defaultData,
    businessType: [],
  });
  const originKeys = ['CHANNEL', 'PRODUCT', 'CAR'];

  const [sumitData, setSumitData] = useState(defaultData);
  const [selectKeys, setSelectKeys] = useState<string[]>([]); //  记录联动筛选的方式
  const [selectedRowKeysChannelList, setSelectedRowKeysChannelList] = useState<string[]>([]); // 渠道勾选受控
  const [selectedRowKeysList, setSelectedRowKeysList] = useState<string[]>([]); // 产品勾选受控
  const [selectedRowKeysCarList, setSelectedRowKeysCarList] = useState<string[]>([]); // 车型勾选受控

  /**
   *  车辆信息表格
   */
  const carInfoColumns = getStaticCarInfoColumns();

  const refreshTable = () => {
    if (selectKeys.length > 0) {
      if (selectKeys.indexOf('CHANNEL') !== 0) {
        ref.CHANNEL.current?.reload();
      }
      if (selectKeys.indexOf('PRODUCT') !== 0) {
        ref.PRODUCT.current?.reload();
      }
      if (selectKeys.indexOf('CAR') !== 0) {
        ref.CAR.current?.reload();
      }
    }
  };

  useEffect(() => {
    refreshTable();
  }, [sumitData]);

  // 按照用户点击顺序来处理联动内容，后级的点击不影响前级的数据，最后一级点击不参与联动
  const handleSelect = (businessType: string) => {
    if (selectKeys.length < 2 && selectKeys.indexOf(businessType) < 0) {
      const data = selectKeys.concat(businessType);
      setSelectKeys(data);
      return {
        status: false,
        query: data.indexOf(businessType) === 0 ? [data[0]] : data,
      };
    }
    return {
      status: selectKeys.indexOf(businessType) < 0,
      query: selectKeys.indexOf(businessType) === 0 ? [selectKeys[0]] : selectKeys,
    };
  };

  const getTickData = (
    keys: string[],
    currentRowKeys?: string[],
    businessType?: string,
    isDelEndData?: boolean,
  ) => {
    // 根据点击顺序判断，若属于第一级，则取selectData的选中数据，否则取sumitData的受控数据
    const tickData = {
      productLaunchChannelList:
        keys.indexOf('CHANNEL') === 0
          ? selectData.current.productLaunchChannelList
          : sumitData.productLaunchChannelList.filter((item: any) => {
              return businessType === 'CHANNEL' && currentRowKeys
                ? currentRowKeys.indexOf(item.id) >= 0
                : selectedRowKeysChannelList.indexOf(item.id) >= 0;
            }),
      productLaunchList:
        keys.indexOf('PRODUCT') === 0
          ? selectData.current.productLaunchList
          : sumitData.productLaunchList.filter((item: any) => {
              return businessType === 'PRODUCT' && currentRowKeys
                ? currentRowKeys.indexOf(item.businessKey) >= 0
                : selectedRowKeysList.indexOf(item.businessKey) >= 0;
            }),
      productLaunchCarList:
        keys.indexOf('CAR') === 0
          ? selectData.current.productLaunchCarList
          : sumitData.productLaunchCarList.filter((item: any) => {
              return businessType === 'CAR' && currentRowKeys
                ? currentRowKeys.indexOf(item.carNo) >= 0
                : selectedRowKeysCarList.indexOf(item.carNo) >= 0;
            }),
      businessType: keys,
    };
    // 末级数据不用参与联动
    if (isDelEndData && keys.length === 2) {
      const key = originKeys.filter((val) => keys.indexOf(val) < 0);
      if (key?.[0]) {
        tickData[`productLaunch${originMaps[key?.[0]]}`] = [];
      }
    }

    return tickData;
  };

  // 多选点击联动方法
  const selectAction = async (props: any) => {
    const { selectedRows, selectedRowKeys, type, businessType } = props;
    const linkSelect = handleSelect(businessType); //  记录点击顺序

    // 参数格式转化
    const arr =
      (selectedRows?.[0] &&
        selectedRows.map((item: any) => {
          if (businessType === 'CHANNEL') {
            return {
              channelName: item.channelName,
              id: item.id,
              status: item?.status,
              tick: true,
            };
          }
          if (businessType === 'PRODUCT') {
            return {
              name: item.name,
              businessKey: item.businessKey,
              status: item.status,
              tick: true,
            };
          }
          return {
            //  CAR
            carNo: item.carNo,
            carModel: item.carModel,
            carModelCode: item.carModelCode,
            statusFlag: item.statusFlag,
            tick: true,
          };
        })) ||
      []; //  获取选中的数据

    selectData.current[`productLaunch${type}`] = arr;

    if (linkSelect.status) return; //  若是最后一级不参与联动

    selectData.current.businessType = linkSelect.query; //  [x]: 查询一级数据，[x, x]: 联动查询一二级数据

    const params = getTickData(linkSelect.query, selectedRowKeys, businessType, true);
    const res = await linkageQuery({
      ...params,
      isQueryCarCity: true,
      isQuerySaleCity: true,
    }).catch((e) => {
      console.log(e);
    }); //  开始联动查询

    if (res?.data) {
      const sendData = { ...sumitData };
      unstable_batchedUpdates(() => {
        if (linkSelect.query.indexOf('CHANNEL') < 0) {
          sendData.productLaunchChannelList = res.data.productLaunchChannelList || [];
          setSelectedRowKeysChannelList([]);
        }
        if (linkSelect.query.indexOf('PRODUCT') < 0) {
          sendData.productLaunchList = res.data.productLaunchList || [];
          setSelectedRowKeysList([]);
        }
        if (linkSelect.query.indexOf('CAR') < 0) {
          sendData.productLaunchCarList = res.data.productLaunchCarList || [];
          setSelectedRowKeysCarList([]);
        }
        setSumitData(sendData); //  提交受控数据
      });
    }
  };

  return (
    <div className={classNames({ [styles.hidden]: !show })}>
      {selectKeys.length > 0 && (
        <div style={{ paddingInline: 4, marginBottom: 12 }}>
          <ProCard bordered>
            <span>联动查询顺序：</span>
            <span style={{ fontWeight: 'bold' }}>
              {selectKeys
                .map((item: any) => {
                  return enToCnMaps[item];
                })
                .join(' -> ')}
            </span>
            <Button
              type="link"
              onClick={() => {
                setSelectKeys([]);
                setSelectedRowKeysCarList([]);
                setSelectedRowKeysChannelList([]);
                setSelectedRowKeysList([]);
                // 刷新表格数据
                refreshTable();
              }}
              style={{
                marginLeft: 15,
              }}
            >
              重置查询方式
            </Button>
          </ProCard>
        </div>
      )}
      <div className={styles.configTable}>
        <Row gutter={[2, 8]}>
          <Col span={8}>
            <ProCard bordered>
              <ProTable
                rowKey={'id'}
                actionRef={ref['CHANNEL']}
                defaultSize={'small'}
                columns={staticChannelColumns}
                search={selectKeys.length > 0 ? false : searchOptions}
                pagination={{
                  defaultPageSize: 10,
                }}
                request={(params) => {
                  if (selectKeys.length === 0 || selectKeys.indexOf('CHANNEL') === 0) {
                    return queryChannel({ ...params, isQuerySaleCity: true });
                  }
                  // const data = queryData(params, sumitData.productLaunchChannelList); //  筛选数据
                  return Promise.resolve({
                    data: sumitData.productLaunchChannelList,
                    success: true,
                  });
                }}
                loading={false}
                options={{
                  density: true,
                }}
                scroll={{ x: 'max-content' }}
                rowSelection={{
                  // hideSelectAll: true,
                  selectedRowKeys: selectedRowKeysChannelList,
                  onChange(selectedRowKeys: any, selectedRows) {
                    setSelectedRowKeysChannelList(selectedRowKeys);
                    selectAction({
                      selectedRows,
                      selectedRowKeys,
                      type: 'ChannelList',
                      businessType: 'CHANNEL',
                    });
                  },
                }}
                tableAlertRender={false}
              />
            </ProCard>
          </Col>
          <Col span={8}>
            <ProCard>
              <ProTable
                rowKey={'businessKey'}
                actionRef={ref['PRODUCT']}
                defaultSize={'small'}
                columns={productColumns}
                search={selectKeys.length > 0 ? false : searchOptions}
                pagination={{
                  defaultPageSize: 10,
                }}
                request={({ interest, ...rest }) => {
                  if (selectKeys.length === 0 || selectKeys.indexOf('PRODUCT') === 0) {
                    const interestTemp: any = {};
                    if (interest) {
                      const { startInterest, endInterest } = interest;
                      if (startInterest || startInterest === 0) {
                        interestTemp.startInterest = Number(new BigNumber(startInterest).div(100));
                      }
                      if (endInterest || endInterest === 0) {
                        interestTemp.endInterest = Number(new BigNumber(endInterest).div(100));
                      }
                    }
                    return getProductByPage({
                      secondaryClassification: 'FINANCE_LEASE',
                      ...rest,
                      ...interestTemp,
                    });
                  }

                  return Promise.resolve({
                    data: sumitData.productLaunchList,
                    success: true,
                  });
                }}
                loading={false}
                options={{
                  density: true,
                }}
                scroll={{ x: 'max-content' }}
                rowSelection={{
                  // hideSelectAll: true,
                  selectedRowKeys: selectedRowKeysList,
                  onChange(selectedRowKeys: any, selectedRows) {
                    setSelectedRowKeysList(selectedRowKeys);
                    selectAction({
                      selectedRows,
                      selectedRowKeys,
                      type: 'List',
                      businessType: 'PRODUCT',
                    });
                  },
                }}
                tableAlertRender={false}
              />
            </ProCard>
          </Col>
          <Col span={8}>
            <ProCard>
              <ProTable
                rowKey={'carNo'}
                actionRef={ref['CAR']}
                defaultSize={'small'}
                columns={carInfoColumns}
                search={selectKeys.length > 0 ? false : searchOptions}
                pagination={{
                  defaultPageSize: 10,
                }}
                request={(params) => {
                  if (selectKeys.length === 0 || selectKeys.indexOf('CAR') === 0) {
                    return queryCarInfoByPage({ ...params, isQueryCarCity: true });
                  }
                  // const data = queryData(params, sumitData.productLaunchCarList); //  筛选数据
                  return Promise.resolve({
                    data: sumitData.productLaunchCarList,
                    success: true,
                  });
                }}
                loading={false}
                options={{
                  density: true,
                }}
                scroll={{ x: 'max-content' }}
                rowSelection={{
                  // hideSelectAll: true,
                  selectedRowKeys: selectedRowKeysCarList,
                  onChange(selectedRowKeys: any, selectedRows) {
                    setSelectedRowKeysCarList(selectedRowKeys);
                    selectAction({
                      selectedRows,
                      selectedRowKeys,
                      type: 'CarList',
                      businessType: 'CAR',
                    });
                  },
                  getCheckboxProps: (record) => ({
                    disabled: record.statusFlag === STATUS.DISABLE,
                  }),
                }}
                tableAlertRender={false}
              />
            </ProCard>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default SearchTableView;
