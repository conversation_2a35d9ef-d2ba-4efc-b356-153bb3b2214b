export enum STATUS {
  ENABLE = 1,
  DISABLE = 0,
  DEL = -1,
}

// 查询视图状态枚举
export enum STATIC_STATUS {
  ENABLE = 1,
  DISABLE = 2,
}

export const CHANNEL_STATUS = new Map([
  [STATUS.ENABLE, '启用'],
  [STATUS.DISABLE, '禁用'],
  // [STATUS.DEL, '删除'],
]);

export const CAR_STATUS = new Map([
  [STATUS.ENABLE, '启用'],
  [STATUS.DISABLE, '禁用'],
  // [STATUS.DEL, '删除'],
]);

export const STATIC_CAR_STATUS = new Map([
  [STATIC_STATUS.ENABLE, '启用'],
  [STATIC_STATUS.DISABLE, '禁用'],
]);

export const CAR_TYPE = new Map([
  [1, '新车'],
  [2, '二手车'],
]);

export const originMaps = {
  CHANNEL: 'ChannelList',
  PRODUCT: 'List',
  CAR: 'CarList',
};

export const enToCnMaps = {
  CHANNEL: '渠道',
  PRODUCT: '产品',
  CAR: '车型',
};
