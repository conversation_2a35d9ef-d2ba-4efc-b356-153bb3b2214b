import { DividerTit } from '@/components';
import HeaderTab from '@/components/HeaderTab';
import { LICENSE_TYPES, LICENSE_TYPES_MAP, LICENSE_TYPES_MAP_ENUM } from '@/enums';
import { downLoadExcel } from '@/utils/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import { ModalForm } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Col, message, Row, Space, Switch } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import BigNumber from 'bignumber.js';
import classNames from 'classnames';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import LicenseTabsCom from '../Product/components/LicenseTabsCom';
import { getCarInfoColumns, getChannelColumns, productColumns } from './columns';
import CarInfoTransfer from './components/CarInfoTransfer';
import ChannelTransfer from './components/ChannelTransfer';
import ConfigModelButton from './components/ConfigModelButton';
import InventoryCityTransfer from './components/InventoryCityTransfer';
import SearchTableView from './components/SearchTableView';
import { STATUS } from './consts';
import type { TCarInfoTableItem, TChannelTableItem, TProductTableItem } from './data.d';
import styles from './index.less';
import {
  exportAllProduct,
  getCarList,
  getChannelList,
  getProductList,
  saveOrDelConfigInfo,
} from './service';

const ProductLaunch: React.FC = () => {
  /**
   * 渠道列表 ref
   */
  const actionChannelRef = useRef<ActionType>();
  /**
   * 车辆列表 ref
   */
  const actionCarRef = useRef<ActionType>();
  /**
   * 保存当前的产品编码
   */
  const [productId, setProductId] = useState<string>('');
  /**
   * 保存当前的产品名称
   */
  const [productName, setProductName] = useState<string>('');
  /**
   * 保存当前的产品上牌类型
   */
  const [currentLicenseType, setCurrentLicenseType] = useState<string>('');
  /**
   * 保存当前的渠道编码
   */
  const [channelId, setChannelId] = useState<number>(NaN);
  /**
   * 保存当前的渠道名称
   */
  const [channelName, setChannelName] = useState<string>('');
  /**
   * 控制 删除渠道配置 弹窗是否显示变量
   */
  const [channelVisible, setChannelVisible] = useState<boolean>(false);
  /**
   * 当前 删除渠道配置 的 id
   */
  const [curDelChannelId, setCurDelChannelId] = useState<number>(NaN);
  /**
   * 控制 删除车辆配置 弹窗是否显示变量
   */
  const [carlVisible, setCarVisible] = useState<boolean>(false);
  /**
   * 当前 删除车辆配置 的 id
   */
  const [curDelCarlId, setCurDelCarId] = useState<number>(NaN);

  const formRef = useRef<FormInstance>();
  const licenseTabsRef = useRef<any>(null);
  const [licenseType, setLicenseType] = useState<string>(LICENSE_TYPES.AFFILIATE);

  const [showSearchView, setShowSearchView] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);

  // 渠道列表
  const channelColumns = getChannelColumns();
  const channelOption = {
    title: '操作',
    valueType: 'option',
    dataIndex: 'city',
    width: 60,
    fixed: 'right',
    render: (_, { id }: { id: number }) => {
      return (
        <Button
          type="link"
          danger
          key="channelDel"
          style={{ padding: 0 }}
          onClick={() => {
            setCurDelChannelId(id);
            setChannelVisible(true);
          }}
        >
          删除
        </Button>
      );
    },
  };
  // 添加操作列
  channelColumns.push(channelOption);

  /**
   *  车辆信息表格
   */
  const carInfoColumns = getCarInfoColumns();
  const carInfoOption = {
    title: '操作',
    dataIndex: 'city',
    valueType: 'option',
    width: 160,
    fixed: 'right',
    render: (_, { id }) => [
      <ConfigModelButton
        key="configModelButton"
        buttonFileProps={{ type: 'link' }}
        configModelButtonParams={{
          buttonText: '配置库存城市',
          children: InventoryCityTransfer,
          getHasConfigList: () =>
            getChannelList({
              parentId: id,
              whetherHasConfig: true,
              businessType: 'CITY',
              current: 1,
              pageSize: 2000,
            }),
          getNotConfigList: () =>
            getChannelList({
              parentId: id,
              whetherHasConfig: false,
              businessType: 'CITY',
              current: 1,
              pageSize: 2000,
            }),
          saveOrDelConfigInfo: (params) =>
            saveOrDelConfigInfo({
              ...params,
              parentId: id,
              businessType: 'CITY',
            }),
          refresh: () => actionCarRef?.current?.reload(),
        }}
      />,
      <Button
        type="link"
        danger
        key="carlDel"
        style={{ padding: 0 }}
        onClick={() => {
          setCurDelCarId(id);
          setCarVisible(true);
        }}
      >
        删除
      </Button>,
    ],
  };
  carInfoColumns.push(carInfoOption);

  const handleExport = async () => {
    setBtnLoading(true);
    try {
      const data = await exportAllProduct();
      if (data) {
        downLoadExcel(data);
      }
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <>
      <HeaderTab />
      <PageContainer
        header={{
          extra: [
            <Space key="action" size={[16, 16]}>
              <Switch
                key="channelSearchBtn"
                checkedChildren="查询视图"
                unCheckedChildren="配置视图"
                onChange={(checked: boolean) => {
                  setShowSearchView(checked);
                }}
              />
              <Button type="primary" key="exportBtn" loading={btnLoading} onClick={handleExport}>
                导出
              </Button>
            </Space>,
          ],
        }}
      >
        <div className={classNames(styles.configTable, { [styles.hidden]: showSearchView })}>
          <Row gutter={[2, 8]}>
            <Col span={8}>
              <ProCard bordered>
                <div className={styles['tool-bar-container']} />
                <ProTable<TProductTableItem>
                  rowKey="businessKey"
                  scroll={{ x: 'max-content' }}
                  // search={false}
                  search={{ span: 12, layout: 'horizontal', defaultCollapsed: false }}
                  columns={productColumns}
                  pagination={{
                    defaultPageSize: 10,
                  }}
                  // toolBarRender={false}
                  request={({ interest, ...rest }) => {
                    const interestTemp: any = {};
                    if (interest) {
                      const { startInterest, endInterest } = interest;
                      if (startInterest || startInterest === 0) {
                        interestTemp.startInterest = Number(new BigNumber(startInterest).div(100));
                      }
                      if (endInterest || endInterest === 0) {
                        interestTemp.endInterest = Number(new BigNumber(endInterest).div(100));
                      }
                    }
                    return getProductList({
                      ...rest,
                      ...interestTemp,
                      secondaryClassification: 'FINANCE_LEASE',
                    });
                  }}
                  rowSelection={{
                    type: 'radio',
                    onChange: (selectedRowKeys, selectedRows) => {
                      setProductName(selectedRows[0].name);
                      setProductId(selectedRows[0].businessKey);
                      setCurrentLicenseType(
                        LICENSE_TYPES[selectedRows[0].registerLicenceType] || '',
                      );
                      actionChannelRef?.current?.clearSelected?.();
                    },
                  }}
                  tableAlertRender={false}
                  formRef={formRef}
                  options={{
                    density: true,
                  }}
                  params={{ registerLicenceType: LICENSE_TYPES_MAP_ENUM[licenseType] }}
                  headerTitle={
                    <LicenseTabsCom
                      defaultVisible={true}
                      tabsRef={licenseTabsRef}
                      onChange={(val) => {
                        setLicenseType(val as string);
                        formRef?.current?.submit();
                      }}
                    />
                  }
                />
              </ProCard>
            </Col>
            <Col span={8}>
              <ProCard bordered>
                {/* <div className={styles['tool-bar-container']}> */}
                <div>
                  <DividerTit title={`产品名称：${productName || '-'}`} />
                </div>
                <div>
                  <DividerTit title={`上牌类型：${LICENSE_TYPES_MAP[currentLicenseType] || '-'}`} />
                </div>
                {/* </div> */}
                <ProTable<TChannelTableItem>
                  rowKey="id"
                  actionRef={actionChannelRef}
                  scroll={{ x: 'max-content' }}
                  columns={channelColumns}
                  pagination={{
                    defaultPageSize: 10,
                  }}
                  search={{ span: 12, layout: 'horizontal', defaultCollapsed: false }}
                  toolBarRender={() => [
                    <ConfigModelButton
                      key="configModelChannel"
                      buttonFileProps={{ type: 'primary', disabled: !productId }}
                      configModelButtonParams={{
                        buttonText: '配置渠道',
                        titleDes: (
                          <div style={{ color: 'red', marginBottom: 8 }}>
                            注：已根据产品对应上牌类型，限制可选渠道范围
                          </div>
                        ),
                        children: ChannelTransfer,
                        disabledOptions: { key: 'licenseTypeList', val: currentLicenseType },
                        getHasConfigList: () =>
                          getChannelList({
                            productCode: productId,
                            businessType: 'CHANNEL',
                            whetherHasConfig: true,
                            current: 1,
                            pageSize: 2000,
                          }),
                        getNotConfigList: () =>
                          getChannelList({
                            productCode: productId,
                            businessType: 'CHANNEL',
                            whetherHasConfig: false,
                            current: 1,
                            pageSize: 2000,
                          }),
                        saveOrDelConfigInfo: (params) =>
                          saveOrDelConfigInfo({
                            ...params,
                            businessType: 'CHANNEL',
                            productCode: productId,
                          }),
                        refresh: () => actionChannelRef?.current?.reload(),
                      }}
                    />,
                  ]}
                  params={{ productCode: productId }}
                  request={(params) => {
                    if (productId) {
                      return getChannelList({
                        ...params,
                        businessType: 'CHANNEL',
                        whetherHasConfig: true,
                        isQuerySaleCity: true,
                      });
                    }
                    return Promise.resolve({
                      data: [],
                      success: true,
                    });
                  }}
                  rowClassName={(record) => {
                    if (record?.status === STATUS.DEL || record?.status === STATUS.DISABLE) {
                      return styles['disable-row'];
                    }
                    return '';
                  }}
                  rowSelection={{
                    type: 'radio',
                    onChange: (selectedRowKeys, selectedRows) => {
                      setChannelId(selectedRows[0]?.id);
                      setChannelName(selectedRows[0]?.name);
                    },
                  }}
                  tableAlertRender={false}
                />
              </ProCard>
            </Col>
            <Col span={8}>
              <ProCard bordered>
                {/* <div className={styles['tool-bar-container']}> */}
                <DividerTit title={`渠道名称：${channelName || '-'}`} />
                {/* </div> */}
                <ProTable<TCarInfoTableItem>
                  rowKey="id"
                  actionRef={actionCarRef}
                  scroll={{ x: 'max-content' }}
                  columns={carInfoColumns}
                  pagination={{
                    defaultPageSize: 10,
                  }}
                  // toolBarRender={false}
                  search={{ layout: 'horizontal', span: 12, defaultCollapsed: false }}
                  options={{
                    density: true,
                  }}
                  rowClassName={(record) => {
                    if (record?.status === STATUS.DEL || record?.status === STATUS.DISABLE) {
                      return styles['disable-row'];
                    }
                    return '';
                  }}
                  toolBarRender={() => {
                    return [
                      <ConfigModelButton
                        key="configModelCar"
                        buttonFileProps={{
                          type: 'primary',
                          disabled: !channelId,
                        }}
                        configModelButtonParams={{
                          buttonText: '配置车辆',
                          titleDes: (
                            <div style={{ color: 'red', marginBottom: 8 }}>
                              注：已根据产品对应上牌类型，限制可选车辆范围
                            </div>
                          ),
                          children: CarInfoTransfer,
                          disabledOptions: { key: 'licenseType', val: currentLicenseType },
                          getHasConfigList: () =>
                            getCarList({
                              parentId: channelId,
                              whetherHasConfig: true,
                              businessType: 'CAR',
                              current: 1,
                              pageSize: 2000,
                            }),
                          getNotConfigList: () =>
                            getCarList({
                              parentId: channelId,
                              whetherHasConfig: false,
                              businessType: 'CAR',
                              current: 1,
                              pageSize: 2000,
                            }),
                          saveOrDelConfigInfo: (params) =>
                            saveOrDelConfigInfo({
                              ...params,
                              businessType: 'CAR',
                              parentId: channelId,
                            }),
                          refresh: () => actionCarRef?.current?.reload(),
                        }}
                      />,
                    ];
                  }}
                  params={{ parentId: channelId }}
                  request={(params) => {
                    if (channelId) {
                      return getCarList({
                        ...params,
                        parentId: channelId,
                        whetherHasConfig: true,
                        businessType: 'CAR',
                      });
                    }
                    return Promise.resolve({
                      data: [],
                      success: true,
                    });
                  }}
                />
              </ProCard>
            </Col>
          </Row>
        </div>
        {/* 查询视图 */}
        <SearchTableView show={showSearchView} />
        {/* 删除渠道配置的弹窗 */}
        <ModalForm
          title="提示"
          width="400px"
          modalProps={{
            centered: true,
            destroyOnClose: true,
          }}
          visible={channelVisible}
          onVisibleChange={setChannelVisible}
          onFinish={async () => {
            await saveOrDelConfigInfo({
              deleteIds: [curDelChannelId],
              businessType: 'CHANNEL',
            });
            message.success('删除成功！');
            setChannelVisible(false);
            if (curDelChannelId === channelId) {
              setChannelId(NaN);
              setChannelName('');
            }
            actionChannelRef?.current?.reload();
          }}
        >
          <>
            <ExclamationCircleOutlined style={{ color: 'red', marginRight: '10px' }} />
            <span>删除后该渠道无法选择此产品进件，配置的车辆数据也将丢失，请谨慎操作!</span>
            <br />
            <br />
            <div>是否确认删除?</div>
          </>
        </ModalForm>
        {/* 删除车辆配置的弹窗 */}
        <ModalForm
          title="提示"
          width="400px"
          modalProps={{
            centered: true,
            destroyOnClose: true,
          }}
          visible={carlVisible}
          onVisibleChange={setCarVisible}
          onFinish={async () => {
            await saveOrDelConfigInfo({
              deleteIds: [curDelCarlId],
              businessType: 'CAR',
            });
            message.success('删除成功！');
            setCarVisible(false);
            actionCarRef?.current?.reload();
          }}
        >
          <>
            <ExclamationCircleOutlined style={{ color: 'red', marginRight: '10px' }} />
            <span>删除后该车辆无法选择此产品进件，配置的库存城市数据也将丢失，请谨慎操作！</span>
            <br />
            <br />
            <div>是否确认删除?</div>
          </>
        </ModalForm>
      </PageContainer>
    </>
  );
};

export default () => {
  return (
    <>
      <HeaderTab />
      <KeepAlive name={'operation-manager/channel/product-launch'}>
        <ProductLaunch />
      </KeepAlive>
    </>
  );
};
