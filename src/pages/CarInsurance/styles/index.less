.car-img-preview {
  :global {
    .ant-image-preview-img-wrapper {
      padding-left: 300px;
      text-align: start;
    }
  }
}

.idcard-upload {
  background: none !important;
  border: none !important;
}

.add-auth {
  :global {
    .ant-form-item {
      margin-bottom: 18px;
    }
  }
}

.preview {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}

.id-card-front:hover {
  .preview {
    display: block;
  }
}

.id-card-back:hover {
  .preview {
    display: block;
  }
}

.display-id-img {
  object-fit: contain;
  aspect-ratio: 1.6;
}

.display-pdf-item {
  & + & {
    margin-top: 6px;
  }
}

.on-way-car-count {
  color: @error-color;
}

.approve-card {
  position: fixed;
  right: 10px;
  bottom: 20px;
  :global {
    .ant-form-item-label {
      text-align: unset;
    }
    .ant-form-item-control {
      flex: auto;
    }
  }
}

.approve-msg {
  display: flex;
  .approve-msg-right {
    flex: 1;
  }
}

.append-info-container {
  position: relative;
  .append-info-btn {
    position: absolute;
    top: 34px;
    right: 0;
  }
}

.car-detail-content + .car-detail-content {
  max-height: 450px;
  margin-top: 25px;
  overflow: auto;
}
