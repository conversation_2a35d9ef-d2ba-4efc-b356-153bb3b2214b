.car-insurance-edit .ant-pro-page-container-children-content {
  margin: 0;
}

.car-edit {
  display: flex;
  gap: 20px;
  justify-content: space-around;
  height: 60vh;
  /* scoll 一直有滚动条 auto 超出才有滚动条 */
  overflow-y: auto;
}

.car-main .ant-tabs-ink-bar {
  display: none;
}

/* .car-main .ant-tabs-nav{
    max-height: 600px;
} */
.car-main .ant-tabs-tab {
  width: 240px !important;
  margin: 0 !important;
  padding: 8px 2px !important;
  text-align: start !important;
}
.car-main .ant-tabs-tab-active {
  background-color: #bae0ff;
}
.car-main .ant-tabs-tab-btn {
  color: #262626 !important;
}

.car-main .ant-descriptions-item-label::after {
  display: none;
}
.car-edit .ant-form-item {
  margin-bottom: 12px;
}
.car-carousel {
  bottom: -12px !important;
  max-width: 100%;
  overflow: auto;
}

.car-carousel button {
  width: 16px !important;
  height: 16px !important;
  background-color: #dadada !important;
  border-radius: 50% !important;
  opacity: 1 !important;
}

.car-carousel .slick-active button {
  background-color: #005bfd !important;
}

.car-carousel .slick-active {
  width: 16px !important;
}

.car-carousel li {
  height: initial !important;
}

.car-carousel .slick-dots li button::after {
  inset: 0;
}
