import { DeleteOutlined, DragOutlined, EyeOutlined } from '@ant-design/icons';
import { useDraggable } from '@dnd-kit/core';
import { Button, Tooltip } from 'antd';
import React, { useCallback, useMemo } from 'react';
import type { DragItem } from '.';

interface DraggableItemProps {
  item: DragItem;
  onDelete?: (itemId: string) => void;
  isDragging?: boolean;
  isSource?: boolean;
}

// 可拖拽的文件项目组件 - 使用React.memo优化重新渲染
const DraggableItem: React.FC<DraggableItemProps> = React.memo(
  ({ item, onDelete, isDragging = false, isSource = false }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      isDragging: isCurrentDragging,
    } = useDraggable({
      id: item.id,
    });

    // 使用useMemo优化样式计算
    const style = useMemo(() => {
      if (!transform) return undefined;
      return {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        opacity: isCurrentDragging ? 0.5 : 1,
      };
    }, [transform, isCurrentDragging]);

    // 处理预览 - 关键：确保事件不被拖拽系统拦截
    const handlePreview = useCallback(
      (e: React.MouseEvent) => {
        // 阻止事件冒泡和默认行为，避免触发拖拽
        e.preventDefault();
        e.stopPropagation();
        // 对于原生事件，使用stopImmediatePropagation
        (e.nativeEvent as Event).stopImmediatePropagation?.();

        if (item.url) {
          // 使用setTimeout确保在拖拽系统处理之后执行
          setTimeout(() => {
            const event = new CustomEvent('drag-item-preview-file', {
              detail: {
                url: item.url,
                fileName: item.name,
              },
            });
            window.dispatchEvent(event);
          }, 0);
        } else {
          console.warn('文件没有URL，无法预览:', item);
        }
      },
      [item.url, item.name],
    );

    // 处理删除
    const handleDelete = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onDelete?.(item.id);
      },
      [onDelete, item.id],
    );

    // // 计算CSS类名
    const className = useMemo(() => {
      return `draggable-file-item ${isCurrentDragging ? 'dragging' : ''} ${isSource ? 'source-item' : 'target-item'
        }`;
    }, [isCurrentDragging, isSource]);

    return (
      <div ref={setNodeRef} style={style} className={className}>
        <div className="file-item-content">
          {/* 只有拖拽手柄区域才能触发拖拽 */}
          <div className="drag-handle" {...attributes} {...listeners}>
            <DragOutlined />
          </div>
          {/* 预览按钮区域不触发拖拽 */}
          <div className="file-icon">
            <Tooltip title="预览">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={handlePreview}
                className="preview-btn"
                onMouseDown={(e) => {
                  // 确保预览按钮不会触发拖拽
                  e.stopPropagation();
                }}
              />
            </Tooltip>
          </div>
          <div className="file-info">
            <div className="file-name">
              <Tooltip title={item.name}>{item.name}</Tooltip>
            </div>
          </div>
          <div className="file-actions">
            {onDelete && (
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={handleDelete}
                  className="delete-btn"
                  onMouseDown={(e) => {
                    // 确保删除按钮不会触发拖拽
                    e.stopPropagation();
                  }}
                />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
    );
  },
);

// 自定义比较函数，只有在关键属性变化时才重新渲染
DraggableItem.displayName = 'DraggableItem';

export default DraggableItem;
