import { useDroppable } from '@dnd-kit/core';
import { Button, Card, InputNumber, Tag } from 'antd';
import React, { useMemo, useState } from 'react';
import { DragItem } from '../DragDropZones';
import DraggableItem from './DraggableItem';

interface SourceAreaProps {
  items: DragItem[];
}

// 源文件区域组件（上方）- 优化性能版本，支持分页查看
const SourceArea: React.FC<SourceAreaProps> = ({ items }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'source',
  });

  // 分页状态 - 优化大数据量性能
  const [currentPage, setCurrentPage] = useState(1);
  const [showAll, setShowAll] = useState(false);
  const itemsPerPage = items.length > 200 ? 32 : 52; // 动态调整每页数量

  // 当文件数量变化时，检查当前页是否还有效
  React.useEffect(() => {
    const totalPages = Math.ceil(items.length / itemsPerPage);
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [items.length, currentPage, itemsPerPage]);

  // 使用useMemo优化渲染性能，支持分页和显示全部
  const renderedItems = useMemo(() => {
    if (items.length === 0) {
      return <div className="empty-source">拖拽文件到此区域</div>;
    }

    // 如果选择显示全部，则显示所有文件
    if (showAll) {
      return (
        <>
          {items.map((item) => (
            <DraggableItem key={item.id} item={item} isSource={true} />
          ))}
          <div className="pagination-controls">
            <div className="pagination-info">显示全部 {items.length} 个文件 (可能影响性能)</div>
            <div className="pagination-buttons">
              <Button
                size="small"
                onClick={() => {
                  setShowAll(false);
                  setCurrentPage(1);
                }}
              >
                返回分页模式
              </Button>
            </div>
          </div>
        </>
      );
    }

    // 分页模式
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const visibleItems = items.slice(startIndex, endIndex);
    const hasMore = items.length > endIndex;
    const hasPrevious = currentPage > 1;
    const totalPages = Math.ceil(items.length / itemsPerPage);

    return (
      <>
        {visibleItems.map((item) => (
          <DraggableItem key={item.id} item={item} isSource={true} />
        ))}
        {(hasMore || hasPrevious || items.length > itemsPerPage) && (
          <div className="pagination-controls">
            <div className="pagination-info">
              第 {currentPage} 页，共 {totalPages} 页 (每页数：{itemsPerPage}，总计 {items.length} 个文件)
            </div>
            <div className="pagination-buttons">
              {hasPrevious && (
                <Button size="small" onClick={() => setCurrentPage((prev) => prev - 1)}>
                  上一页
                </Button>
              )}
              {totalPages > 1 && (
                <div className="page-jumper">
                  <InputNumber
                    size="small"
                    min={1}
                    max={totalPages}
                    value={currentPage}
                    onChange={(value) => {
                      if (value && value >= 1 && value <= totalPages) {
                        setCurrentPage(value);
                      }
                    }}
                    style={{ width: 60 }}
                  />
                  <span className="page-jumper-text">/ {totalPages}</span>
                </div>
              )}
              {hasMore && (
                <Button
                  size="small"
                  type="primary"
                  onClick={() => setCurrentPage((prev) => prev + 1)}
                >
                  下一页 ({items.length - endIndex} 个文件)
                </Button>
              )}
              {items.length > itemsPerPage && (
                <Button
                  size="small"
                  type="dashed"
                  onClick={() => setShowAll(true)}
                  title="显示全部文件，可能影响性能"
                >
                  显示全部
                </Button>
              )}
            </div>
          </div>
        )}
      </>
    );
  }, [items, currentPage, showAll]);

  // 使用useMemo优化CSS类名计算
  const containerClassName = useMemo(() => {
    return `source-area ${isOver ? 'source-over' : ''}`;
  }, [isOver]);

  return (
    <div className={containerClassName}>
      <Card
        title={
          <div className="area-header">
            <span className="area-header-title">
              以下文件无法匹配到对应车辆，请手动拖放到下方对应车辆位置
            </span>
            <Tag color="blue">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="source-files-container">
          <div className="source-files-grid">{renderedItems}</div>
        </div>
      </Card>
    </div>
  );
};

export default React.memo(SourceArea);
