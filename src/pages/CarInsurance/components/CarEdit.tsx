import { ModalForm, ProFormDatePicker, ProFormDigit, ProFormText } from '@ant-design/pro-form';

import { useModel } from '@umijs/max';
import { Button, Form } from 'antd';
import dayjs from 'dayjs';
import type { ReactElement } from 'react';
import React, { memo, useState } from 'react';
import type { IcarInfoItem } from '../type';
import { getRandomUUID, verifyCarInfo } from '../utils';
import './CarEdit.less';
import CarUploadDraggerFormItem from './CarUploadDraggerFormItem';
type CarEditProps = {
  children: ReactElement;
  carInfoItem?: IcarInfoItem;
  title: string;
};
const amountLimit = 80000;
const errorMsg = () => Promise.reject(new Error(`范围0-${amountLimit},最多保留两位小数`));

const CarEdit: React.FC<CarEditProps> = (props) => {
  const [form] = Form.useForm<IcarInfoItem>();
  const { carInfoItem, title } = props;
  const [visible, setVisible] = useState(false);
  const { setCarInfo, carInfo, isMultiplexOrder, detailData } = useModel(
    'CarInsurance.carInsurance',
  );
  // 投保单车辆不一致提示
  const [visibleTip, setVisibleTip] = useState(false);
  const handleTipVisible = (visible: boolean) => {
    setVisibleTip(visible);
  };

  async function onFinish(values: IcarInfoItem) {
    console.log('valuesvalues', values);

    const { commercialInsuranceEndDate, commercialInsuranceStartDate } = values;
    const commercialInsurancePeriod =
      commercialInsuranceEndDate && commercialInsuranceStartDate
        ? dayjs(commercialInsuranceEndDate).diff(commercialInsuranceStartDate, 'day')
        : (undefined as any);
    values.commercialInsurancePeriod = commercialInsurancePeriod;
    if (carInfoItem?.uuid) {
      // 编辑
      const carInfo1 = carInfo.map((item) => {
        if (item?.uuid === carInfoItem?.uuid) {
          // 编辑的时候对当前修改的车辆进行校验
          const newItem = {
            ...carInfoItem,
            ...values,
          };
          return newItem;
        } else {
          return item;
        }
      });
      verifyCarInfo(carInfo1);
      setCarInfo(carInfo1);
    } else {
      // 新增
      values.uuid = getRandomUUID();
      setCarInfo(verifyCarInfo([values, ...carInfo]));
    }
    setVisible(false);
  }
  return (
    <ModalForm<IcarInfoItem>
      title={title}
      visible={visible}
      width={1000}
      trigger={
        <div
          onClick={() => {
            setVisible(true);
          }}
        >
          {props.children}
        </div>
      }
      layout="horizontal"
      labelCol={{ span: 10 }}
      wrapperCol={{ span: 14 }}
      initialValues={carInfoItem}
      form={form}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => setVisible(false),
      }}
      submitter={{
        render: (action, defaultDoms) => {
          return [
            <Button key="cancel" onClick={() => setVisible(false)}>
              取消
            </Button>,
            <Button
              key="saveDraft"
              type="primary"
              onClick={() => {
                const values = form.getFieldsValue();
                const { commercialInsuranceEndDate, commercialInsuranceStartDate } = values;
                values.commercialInsuranceEndDate = commercialInsuranceEndDate
                  ? `${dayjs(commercialInsuranceEndDate).format('YYYY-MM-DD')} 00:00:00`
                  : (undefined as any);
                values.commercialInsuranceStartDate = commercialInsuranceStartDate
                  ? `${dayjs(commercialInsuranceStartDate).format('YYYY-MM-DD')} 00:00:00`
                  : (undefined as any);
                onFinish(values);
              }}
            >
              {/* 保存草稿 */}
              确定
            </Button>,

            // ...defaultDoms,
          ];
        },
      }}
      onFinish={onFinish}
    >
      <div className="car-edit">
        <div style={{ width: '480px' }}>
          <div>
            <div>车辆证件</div>
            <Form.Item wrapperCol={{ span: 24 }} name="vehicleLicenseUrlList">
              <CarUploadDraggerFormItem
                form={form}
                orderNo={isMultiplexOrder ? undefined : detailData?.orderNo}
                ossType={'vehicleLicenseOssUrlList'}
                ossUrlList={carInfoItem?.vehicleLicenseOssUrlList}
              />
            </Form.Item>
          </div>

          <div>
            <div className="tip-wrpper-flex">
              投保单
              {visibleTip && (
                <div className="tip-text">投保材料中车架号/车牌号与车辆证件不一致</div>
              )}
            </div>
            <p className="ocr-tip-text">保单OCR功能当前仅供试用，成功率较低，请注意核实识别结果</p>
            <Form.Item wrapperCol={{ span: 24 }} name="insuranceSlipUrlList">
              <CarUploadDraggerFormItem
                form={form}
                orderNo={isMultiplexOrder ? undefined : detailData?.orderNo}
                ossType={'insuranceSlipOssUrlList'}
                ossUrlList={carInfoItem?.insuranceSlipOssUrlList}
                handleTipVisible={handleTipVisible}
              />
            </Form.Item>
          </div>
        </div>

        <div style={{ width: '400px' }}>
          <ProFormText
            name="plateNo"
            label="车牌号/新车合格证号"
            placeholder="请输入"
            rules={[
              { required: true },
              {
                pattern: /^[0-9a-zA-Z\u4e00-\u9fa5]+$/,
                message: '只能由数字,字母,汉字组成',
              },
            ]}
          />
          <ProFormText
            name="vin"
            label="车架号"
            placeholder="请输入名称"
            rules={[
              { required: true },
              {
                pattern: /^[A-HJ-NPR-Z0-9]{17}$/,
                message: '只能由大写字母,数字,I/O/Q除外的17位字符组成',
              },
            ]}
          />
          <ProFormText
            name="engineNumber"
            label="发动机号"
            placeholder="请输入名称"
            rules={[
              { required: true },
              {
                pattern: /^[0-9a-zA-Z\u4e00-\u9fa5]+$/,
                message: '只能由数字,字母,汉字组成',
              },
            ]}
          />
          <ProFormText
            name="vehicleLicenseOwner"
            label="行驶证车主"
            placeholder="请输入"
            rules={[{ required: true }, { min: 2, message: '不能少于2个字' }]}
          />

          <ProFormText
            name="assuredName"
            label="被保险人"
            placeholder="请输入"
            rules={[{ required: true }, { min: 2, message: '不能少于2个字' }]}
          />
          <ProFormDatePicker
            name="commercialInsuranceStartDate"
            label="商业险开始日期"
            rules={[{ required: true }]}
            transform={(value) => {
              // 提交的时候触发了
              return {
                commercialInsuranceStartDate: `${dayjs(value).format('YYYY-MM-DD')} 00:00:00`,
              };
            }}
          />
          <ProFormDatePicker
            name="commercialInsuranceEndDate"
            label="商业险结束日期"
            transform={(value) => {
              // 提交的时候触发了 保存草稿不会触发
              return {
                commercialInsuranceEndDate: `${dayjs(value).format('YYYY-MM-DD')} 23:59:59`,
              };
            }}
            rules={[
              { required: true },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  // 得到一个固定时间戳 ms  格式不正确返回NaN
                  const startTime = dayjs(getFieldValue('commercialInsuranceStartDate')).valueOf();
                  const endTime = dayjs(value).valueOf();
                  const rangeTime = endTime - startTime;
                  if (rangeTime <= 24 * 60 * 60 * 1000 || rangeTime > 367 * 24 * 60 * 60 * 1000) {
                    return Promise.reject(
                      new Error('结束日期必须大于开始日期且结束日期不能大于开始日期367天'),
                    );
                  } else {
                    return Promise.resolve();
                  }
                },
              }),
            ]}
          />
          <ProFormDigit
            name="commercialInsurancePremium"
            label="商业险保费(元)"
            placeholder="请输入"
            rules={[
              { required: true },
              ({}) => ({
                validator(_, value) {
                  if (value >= 0 && value <= amountLimit) {
                    return Promise.resolve();
                  } else {
                    return errorMsg();
                  }
                },
              }),
            ]}
            fieldProps={{
              precision: 2,
            }}
          />
          <ProFormDigit
            name="compulsoryInsurancePremium"
            label="交强险保费(元)"
            placeholder="请输入"
            rules={[
              { required: true },
              ({}) => ({
                validator(_, value) {
                  if (value >= 0 && value <= amountLimit) {
                    return Promise.resolve();
                  } else {
                    return errorMsg();
                  }
                },
              }),
            ]}
            fieldProps={{
              precision: 2,
            }}
          />
          <ProFormDigit
            name="vehicleTaxAmount"
            label="车船税金额(元)"
            placeholder="请输入"
            rules={[
              { required: true },
              ({}) => ({
                validator(_, value) {
                  if (value >= 0 && value <= amountLimit) {
                    return Promise.resolve();
                  } else {
                    return errorMsg();
                  }
                },
              }),
            ]}
            fieldProps={{
              precision: 2,
            }}
          />
          <ProFormDigit
            name="otherPaymentAmount"
            label="其他代付金额(元)"
            placeholder="请输入"
            rules={[
              { required: true },
              ({}) => ({
                validator(_, value) {
                  if (value >= 0 && value <= amountLimit) {
                    return Promise.resolve();
                  } else {
                    return errorMsg();
                  }
                },
              }),
            ]}
            fieldProps={{
              precision: 2,
            }}
          />

          <div style={{ display: 'none' }}>
            <ProFormText
              name="vehicleLicenseOssUrlList"
              label="车辆行驶证/登记证/合格证Oss相对url"
            />
            <ProFormText name="commercialInsurancePeriod" label="商业险保单期限" />
            <ProFormText name="id" label="序号" />
            <ProFormText name="insuranceSlipOssUrlList" label="投保单Oss相对url" />
          </div>
        </div>
      </div>
    </ModalForm>
  );
};
export default memo(CarEdit);
