import { CarInsuranceStatusMap, EcarInsuranceStatus } from '@/utils/bankend/enum';
import { history, useAccess, useModel } from '@umijs/max';
import { Alert, Typography } from 'antd';
import React, { memo } from 'react';
import styles from '../styles/index.less';
import type { IoperateRecordItem } from '../type';
const { Title } = Typography;

const ApproveInfo = () => {
  const { detailData } = useModel('CarInsurance.carInsurance');
  const { orderNo } = history.location.query;
  const operateRecord: IoperateRecordItem[] = detailData.operateLogs;
  const rejectReason = operateRecord?.filter((item) => item.status === EcarInsuranceStatus.REJECT);
  const remarkReason = operateRecord?.filter((item) => item.status === EcarInsuranceStatus.SIGN);
  const access = useAccess();
  const status: EcarInsuranceStatus = detailData.orderStatus;
  const { initialState = {} } = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode } = currentUser;
  /**
   * 校验权限 是否要展示驳回原因
   *   // 1. 当订单状态为待审批时，可审批的角色查看订单详情时会看到历史驳回原因, 渠道用户不用看
  // 2. 渠道用户只用在审核驳回的时候看
  // 3. 可审批的角色 也可以看审核驳回看
  // 也就是审核驳回状态 不做权限校验
  // 待审核需要做权限校验
   * @returns {boolean} true 可以展示 false 不可以展示
   */
  function verify() {
    if (rejectReason?.length) {
      // 有驳回原因
      if (status === EcarInsuranceStatus.REJECT) {
        return true;
      }
      if (status === EcarInsuranceStatus.PENDING && !channelCode) {
        return true;
      }
    }
    return false;
  }
  const renderAlert = (type, time, msg) => {
    const typeMap = {
      reject: {
        type: 'error',
        text: '驳回原因',
      },
      remark: {
        type: 'info',
        text: '内部备注',
      },
    };
    return (
      <div key={time} style={{ marginTop: 6 }}>
        <Alert
          message={
            <div className={styles['approve-msg']}>
              <div>
                <span style={{ marginRight: 10 }}>{time}</span>
                <span>{typeMap[type].text}：</span>
              </div>
              <div className={styles['approve-msg-right']}>
                <span style={{ whiteSpace: 'pre-wrap' }}>{msg}</span>
              </div>
            </div>
          }
          type={typeMap[type].type}
        />
      </div>
    );
  };
  return (
    <div>
      <div>
        <span>
          当前状态:{' '}
          <h3 style={{ display: 'inline' }}> {CarInsuranceStatusMap[status] || '草稿'}</h3>
        </span>
        {/* 复用订单时不显示订单号 */}
        {!!orderNo && (
          <span style={{ marginLeft: 20 }}>
            订单号：
            <Title
              code={false}
              style={{ display: 'inline' }}
              copyable={{ text: (orderNo as string) || '' }}
              level={5}
            >
              {orderNo || '暂无订单号'}
            </Title>
          </span>
        )}
      </div>
      {verify()
        ? rejectReason.map((item) => {
            const { auditTime, approvalMsg } = item;
            return renderAlert('reject', auditTime, approvalMsg);
          })
        : null}
      {access.hasAccess('show_remark_msg_detail_car_insurance_list')
        ? remarkReason?.map((item) => {
            const { auditTime, approvalMsg } = item;
            // 已有数据兼容
            return approvalMsg ? renderAlert('remark', auditTime, approvalMsg) : null;
          })
        : null}
    </div>
  );
};
export default memo(ApproveInfo);
