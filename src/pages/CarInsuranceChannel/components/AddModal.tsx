import { EcarInsuranceChannelType } from '@/utils/bankend/enum';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { useAccess, useModel } from '@umijs/max';
import { Form, message, Radio } from 'antd';
import type { ReactElement } from 'react';
import React, { memo, useRef, useState } from 'react';
import type { IgetListParams } from '../services';
import { add, getList } from '../services';
import type { IcarInsuranceChannelItem } from '../type';
import { getPersonalOrEnterpriseHasVal } from '../util';
import CorRelation from './CorRelation';
type Props = {
  children: ReactElement;
  action: ActionType;
};
const AddModal: React.FC<Props> = (props) => {
  const { initialState } = useModel<'@@initialState'>('@@initialState');
  const { channelLevel, channelCode, channelName } = initialState?.currentUser || {};
  const corRelationRef = useRef<any>();
  const [currentParentChannelInfo, setCurrentParentChannelInfo] = useState<Record<string, any>>({
    channelCode,
    channelName,
  });
  // 重制组件
  const [corRelationKey, setCorRelationKey] = useState(1);
  const access = useAccess();

  const formRef = useRef<ProFormInstance>();
  const handleResetCorRelation = () => {
    formRef?.current?.resetFields([
      'relatedProductAndEnterpriseFlat',
      'personalRelatedProduct',
      'personalRelatedPayAccount',
    ]);
    setCorRelationKey((x) => x + 1);
  };
  return (
    <ModalForm<IcarInsuranceChannelItem>
      title="新增车险分期渠道"
      formRef={formRef}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 14 }}
      modalProps={{
        destroyOnClose: true,
      }}
      layout="horizontal"
      trigger={props.children}
      onFinish={async (values) => {
        const personalRelatedPayAccount = formRef?.current?.getFieldValue(
          'personalRelatedPayAccount',
        );
        const { personal, enterprise } = getPersonalOrEnterpriseHasVal(values);
        let personalRelatedPayAccountT, relatedPayAccountT;
        //个人配置公用保司选择，后端字段依旧沿用两个。需要根据产品与企业的是否选中判断一下
        if (personal) {
          personalRelatedPayAccountT = personalRelatedPayAccount;
        }
        if (enterprise) {
          relatedPayAccountT = personalRelatedPayAccount;
        }
        let parentChannelCode: undefined | string = undefined;
        // 运营/运营主管/一级渠道 新建二级渠道是用当前父渠道code
        if (values.channelLevel === 2) {
          parentChannelCode = currentParentChannelInfo.channelCode;
        }
        await add({
          ...values,
          personalRelatedPayAccount: personalRelatedPayAccountT,
          relatedPayAccount: relatedPayAccountT,
          parentChannelCode,
        });
        message.success('提交成功');
        props?.action.reload();
        return true;
      }}
      onValuesChange={(val, values) => {
        //  校验规则 有个全选下拉框 无法触发onValuesChange 由于setfieldsvalue
        corRelationRef.current?.onCheckRules(values);
      }}
      initialValues={{
        relatedProductAndEnterpriseFlat: [{}],
        channelLevel: channelLevel === 1 ? 2 : 1,
      }}
    >
      <Form.Item label="渠道层级" required name="channelLevel">
        <Radio.Group
          onChange={() => {
            handleResetCorRelation();
            setCurrentParentChannelInfo({ channelCode, channelName });
          }}
        >
          <Radio value={1} disabled={channelLevel === 1}>
            一级
          </Radio>
          <Radio
            value={2}
            disabled={
              !channelLevel && !access.hasAccess('opt_subordinate_car_insurance_channel_list')
            }
          >
            二级
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item noStyle dependencies={['channelLevel']}>
        {({ getFieldValue }) => {
          const level = getFieldValue('channelLevel');
          return level === 2 && channelLevel !== 1 ? (
            <ProFormSelect
              debounceTime={500}
              placeholder="请选择上级渠道"
              fieldProps={{
                allowClear: false,
                filterOption: false, // 远程搜索一定要加这个
                onSelect(value, { channelCode, channelName }) {
                  handleResetCorRelation();
                  setCurrentParentChannelInfo({ channelCode, channelName });
                },
              }}
              request={async (values) => {
                const keyWords = values?.keyWords?.trim();
                if (!keyWords) return [];
                const res = await getList({
                  channelName: keyWords,
                  levelList: [1],
                } as IgetListParams);
                return res?.data.map(({ channelCode, channelName }) => {
                  return {
                    label: channelName,
                    value: channelCode,
                    channelCode,
                    channelName,
                  };
                });
              }}
              rules={[{ required: true }]}
              showSearch
              name="parentChannelCode"
              label="上级渠道"
            />
          ) : null;
        }}
      </Form.Item>
      <ProFormSelect
        name="channelType"
        label="渠道类型"
        placeholder="请选择渠道类型"
        rules={[{ required: true }]}
        options={Object.keys(EcarInsuranceChannelType).map((item) => {
          return {
            label: EcarInsuranceChannelType[item],
            value: item,
          };
        })}
      />
      <ProFormText
        name="channelShortName"
        label="渠道简称"
        placeholder="请输入渠道简称"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="channelName"
        label="渠道全称"
        placeholder="请输入渠道全称"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="orgCode"
        label="社会统一信用代码"
        placeholder="请输入社会统一信用代码"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="contactName"
        label="联系人"
        placeholder="请输入联系人"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="contactPhone"
        label="联系人电话"
        placeholder="请输入联系人电话"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="contactAddress"
        label="联系人地址"
        placeholder="请输入联系人地址"
        required
        rules={[{ required: true }]}
      />
      <CorRelation
        key={corRelationKey}
        playground="add"
        corRelationRef={corRelationRef}
        isSecondList={formRef?.current?.getFieldValue('channelLevel') === 2}
        parentChannelInfo={currentParentChannelInfo}
        formRef={formRef}
      />
    </ModalForm>
  );
};
export default memo(AddModal);
