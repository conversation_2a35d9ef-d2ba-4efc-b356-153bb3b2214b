/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-20 17:08:30
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-02-24 18:04:33
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/repay-regist.tsx
 * @Description: repay-regist
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import {
  ADVANCED_STATUS,
  CLASSIFICATION,
  REPAY_NODE_STATUS,
  SECONDARY_CLASSIFICATION,
} from '@/enums';
import { getAllChannelNameEnum } from '@/services/enum';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { disableFutureDate, isChannelStoreUser } from '@/utils/utils';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Link, useAccess } from '@umijs/max';
import { Button, message } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { getProductNameEnum } from '../CarInsurance/services';
import { getStoreAndApplyCityAll, recordingExportAsync, repayRegistList } from './services';
import type { RepayRegistItem } from './types';
import { secondaryClassificationEnCodeMap } from './types';

type Props = {
  classification: string;
  secondaryClassification?: string;
};

const repayTypeEnum = {
  1: '主动还款',
  2: '代扣还款',
  3: '退单还款',
  4: '催收减免',
  5: '催收回款',
  6: '抵扣还款',
  7: '溢缴款冲抵',
  8: '线下还款',
  9: '人工后台还款',
  10: '代偿还款',
  11: '线下还款减免',
  12: '专属账号还款',
  15: '线上还款', //  ONLINE_REMIT(15, "线上还款")
  16: '线上还款减免', //  ONLINE_REMISSION(16, "线上还款减免")
  17: '减保预约入账',
  13: '退保结清',
};

const allRepayTypeEnum = {
  1: '主动还款',
  2: '代扣还款',
  3: '退单还款',
  4: '催收减免',
  5: '催收回款',
  6: '抵扣还款',
  7: '溢缴款冲抵',
  8: '线下还款',
  9: '人工后台还款',
  10: '代偿还款',
  11: '线下还款减免',
  12: '专属账号还款',
  14: '易人行差额垫付',
  15: '线上还款', //  ONLINE_REMIT(15, "线上还款")
  16: '线上还款减免', //  ONLINE_REMISSION(16, "线上还款减免")
  17: '减保预约入账',
  13: '退保结清',
};
const RepayRegist: React.FC<Props> = (props) => {
  const { classification, secondaryClassification } = props;
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [isCarInsurance, setIsCarInsurance] = useState(false); // 二级分类是否是车险
  const [secondProduct, setSecondProduct] = useState(
    isChannelStoreUser(access) ? 'FINANCE_LEASE' : '',
  );

  // 所有的
  const [productOptions, setProductOptions] = useState<{ value: string; label: string }[]>([]);
  // 不同二级分类下的
  const [currentProductOptions, setCurrentProductOptions] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    //融租渠道用户不需要产品搜索
    if (!isChannelStoreUser(access) && secondaryClassification) {
      // 获取 设置 所有的产品选项
      getProductNameEnum(secondaryClassification).then((res) => {
        setProductOptions(res);
      });
    }
  }, [secondaryClassification]);

  useEffect(() => {
    setIsCarInsurance(secondaryClassification === 'CAR_INSURANCE');
    if (secondaryClassification) {
      setSecondProduct(secondaryClassification);
    }
  }, [secondaryClassification]);

  // 当一级和二级分类发生变化 且都存在时 触发提交
  // 一级和二级发生变化，对应产品名称也会变化，所以要情况产品名称，让用户重新选择
  useEffect(() => {
    formRef.current?.setFieldValue('productCode', undefined);
    if (classification && secondaryClassification) {
      formRef.current?.submit();
    }
  }, [classification, secondaryClassification]);

  // 设置二级分类对应的产品名称 二级分类不存在设置为空[]
  useEffect(() => {
    if (secondaryClassification && productOptions.length) {
      const twoCode = secondaryClassificationEnCodeMap[secondaryClassification];
      setCurrentProductOptions(
        productOptions.filter((product) => product.value.substring(0, 4) === twoCode),
      );
    } else {
      setCurrentProductOptions([]);
    }
  }, [secondaryClassification, productOptions]);

  // 缓存
  const getStoreAndApplyCityAllMemo = useMemo(async () => {
    return await getStoreAndApplyCityAll();
  }, []);
  const getAllChannelNameEnumMemo = useMemo(async () => {
    return await getAllChannelNameEnum();
  }, []);

  console.log('access', access);

  // leaseStoreUser    融租门店用户 有一个固定的门店 access?.currentUser?.extSource?.storeId
  // leaseChannelUser  融租渠道用户
  // 需要初始化表单值
  useEffect(() => {
    if (isChannelStoreUser(access)) {
      if (access?.currentUser?.channelCode) {
        // 初始化表单值 渠道名称
        formRef?.current?.setFieldValue('channelIds', [access.currentUser.channelCode]);
      }
      if (access.currentUser?.extSource?.storeId) {
        formRef.current?.setFieldValue('storeIds', [
          access?.currentUser?.extSource?.storeId?.toString(),
        ]);
      }

      if (access.currentUser?.channelType) {
        formRef.current?.setFieldValue('channelTypes', access.currentUser.channelType.toString());
      }
    }
  }, [access]);

  // leaseStoreUser    融租门店用户 有一个固定的门店 access?.currentUser?.extSource?.storeId
  // leaseChannelUser  融租渠道用户
  // 有一些额外的请求请求参数的设置
  const getChannelStoreRequestParams = (access: any) => {
    const params: any = {};
    if (isChannelStoreUser(access)) {
      // 渠道类型
      if (access.currentUser.channelType) {
        params.channelTypes = access.currentUser.channelType;
      }
      // 渠道
      if (access.currentUser?.channelCode) params.channelIds = [access.currentUser.channelCode];
      // 门店
      if (access.currentUser?.extSource?.storeId)
        params.storeIds = [access.currentUser.extSource.storeId];
    }
    return params;
  };

  const columns: ProColumns<RepayRegistItem>[] = [
    {
      title: '还款编号',
      key: 'repayPlanNo',
      dataIndex: 'repayPlanNo',
    },
    {
      title: '期数',
      key: 'termDetail',
      dataIndex: 'termDetail',
      search: false,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      fixed: 'left',
      width: '170px',
      render: (text, record) => {
        const toUrl = `/businessMng/postLoanMng/after-loan-detail?orderNo=${
          record.orderNo
        }&productCode=${record.productCode}&termDetail=${
          record.termDetail ? record.termDetail.split('/')[0] : 0
        }`;
        const toCarInsuranceUrl = `/businessMng/postLoanMng/car-insurance/detail?orderNo=${text}`;
        let dom: React.ReactNode = '-';
        if (access.hasAccess('repayment_detail_postLoanMng_afterLoanList')) {
          dom = (
            <Link to={record.productCode?.substring(0, 4) === '0303' ? toCarInsuranceUrl : toUrl}>
              {record.orderNo}
            </Link>
          );
        } else {
          dom = (
            <Button disabled type="link">
              {record.orderNo}
            </Button>
          );
        }
        return dom;
      },
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
    },
    {
      dataIndex: 'licensePlateNo',
      title: '车牌号',
      hideInTable: !isCarInsurance,
      search: false,
    },
    {
      dataIndex: 'vin',
      title: '车架号',
      hideInTable: !isCarInsurance,
      search: false,
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      initialValue: 'FINANCE_LEASE',
      valueType: 'select',
      valueEnum: CLASSIFICATION,
      search: false,
      // fieldProps: {
      //   allowClear: false,
      //   disabled: isChannelStoreUser(access),
      //   onChange(value: string) {
      //     if (value === 'SMALL_LOAN') {
      //       formRef.current?.setFieldValue('secondaryClassification', 'CAR_INSURANCE');
      //       setIsCarInsurance(true);
      //       setIsAllowClear(false);
      //     } else if (value === 'FINANCE_LEASE') {
      //       formRef.current?.setFieldValue('secondaryClassification', 'FINANCE_LEASE');
      //       setIsAllowClear(false);
      //     } else {
      //       setIsAllowClear(true);
      //     }
      //   },
      // },
      hideInTable: true,
    },
    {
      title: '产品一级分类',
      dataIndex: 'productFirstTypeName',
      search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      valueType: 'select',
      initialValue: 'FINANCE_LEASE', //  融租渠道用户筛选小圆车融',
      valueEnum: SECONDARY_CLASSIFICATION,
      search: false,
      hideInTable: true,
      // fieldProps: {
      //   onChange: (value: string) => {
      //     setIsCarInsurance(value === 'CAR_INSURANCE');
      //     setSecondProduct(value);
      //   },
      //   allowClear: isAllowClear,
      //   disabled: isChannelStoreUser(access),
      // },
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      search: !isChannelStoreUser(access) as any, //  渠道门店帐号不需展示产品名称筛选
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: currentProductOptions,
      },
      hideInTable: true,
    },
    {
      title: '还款方式',
      dataIndex: 'repayTypeList',
      search: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      valueEnum: isChannelStoreUser(access) ? repayTypeEnum : allRepayTypeEnum,
      hideInTable: true,
    },
    {
      title: '投保主体',
      dataIndex: 'insuranceSubject',
      search: false,
      hideInTable: !isCarInsurance,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '资金渠道',
      dataIndex: 'loanChannelCode',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '融租渠道类型',
      dataIndex: 'leaseChannelType',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '渠道名称',
      dataIndex: 'leaseChannelName',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '门店',
      dataIndex: 'leaseStoreName',
      search: false,
      hideInTable: secondProduct !== 'FINANCE_LEASE',
    },
    {
      title: '还款流水号',
      key: 'recordingNo',
      dataIndex: 'recordingNo',
      search: false,
    },
    {
      title: '还款总额',
      key: 'actualRepaymentAmount',
      dataIndex: 'actualRepaymentAmount',
      search: false,
    },
    {
      title: '还款本金',
      key: 'principal',
      dataIndex: 'principal',
      search: false,
    },
    {
      title: '还款利息',
      key: 'interest',
      dataIndex: 'interest',
      search: false,
    },
    {
      title: '还款罚息',
      key: 'penaltyInterest',
      dataIndex: 'penaltyInterest',
      search: false,
    },
    {
      title: '还款其他费用',
      key: 'cost',
      dataIndex: 'cost',
      search: false,
    },
    {
      title: '还款方式',
      dataIndex: 'repayTypeStr',
      search: false,
    },
    {
      title: '还款时间',
      dataIndex: 'repayTime',
      key: 'repayTime',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      search: {
        transform: (value: any) => ({
          repayStartTime: `${value[0]}`,
          repayEndTime: `${value[1]}`,
        }),
      },
      render(_, record) {
        return record.repayTime || '-';
      },
    },
    {
      title: '支付申请单号',
      dataIndex: 'borderNo',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'payChannelName',
      search: false,
    },
    {
      title: '收款账号',
      dataIndex: 'merchantId',
      search: false,
    },
    {
      title: '支付流水号',
      dataIndex: 'instPayNo',
      search: false,
    },
    {
      title: '三方还款流水号',
      dataIndex: 'bankSerialNo',
      search: false,
    },
    {
      title: '支付金额',
      dataIndex: 'paymentAmount',
      search: false,
      render(_, record) {
        const { combineAmount, paymentAmount } = record;
        const _paymentAmount = new BigNumber(paymentAmount).dividedBy(100).toString();
        const _combineAmount = new BigNumber(combineAmount).dividedBy(100).toString();
        const combineAmountJsx = combineAmount > 0 ? '/' + _combineAmount : '';
        return _paymentAmount + combineAmountJsx;
      },
    },
    {
      title: '资方实收',
      dataIndex: 'capitalAmount',
      search: false,
      render(_, record) {
        const { repayType, repayNode, capitalAmount } = record;
        // 非融租业务展示‘-’
        if (
          capitalAmount === undefined ||
          capitalAmount === null ||
          secondProduct !== 'FINANCE_LEASE'
        )
          return '-';
        // 还款方式为催收减免，线下还款减免，易人行垫付，线上还款减免时展示‘-’
        // 还款节点为回购后，展示‘-’
        const showRealValue1 = repayType && [4, 11, 14, 16].includes(repayType);
        const showRealValue2 = repayNode === 2;
        return showRealValue1 || showRealValue2 ? '-' : capitalAmount;
      },
    },
    {
      title: '易人行逾期垫付',
      dataIndex: 'advancedStatus',
      key: 'advancedStatus',
      search: false,
      valueEnum: ADVANCED_STATUS,
      hidden: isChannelStoreUser(access),
    },
    {
      title: '还款节点',
      dataIndex: 'repayNode',
      search: false,
      render(_, record) {
        // 非融租业务展示‘-’
        return secondProduct === 'FINANCE_LEASE' ? REPAY_NODE_STATUS[record?.repayNode || 0] : '-';
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      params: { key: 'channelIds_repay_regist' },
      valueType: 'select',
      search: secondProduct !== 'FINANCE_LEASE' ? false : undefined,
      request: () => {
        return getAllChannelNameEnumMemo.then((res: any) => {
          return res;
        });
      },
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.channelCode,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '门店',
      dataIndex: 'storeIds',
      params: { key: 'storeIds_repay_regist' },
      hideInTable: true,
      search: secondProduct !== 'FINANCE_LEASE' ? false : undefined,
      request: () => {
        return getStoreAndApplyCityAllMemo.then((res) => {
          return (
            res?.data?.storeList?.map((item: { storeName: string; id: string }) => {
              return { value: item.id?.toString(), label: item.storeName };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.extSource?.storeId,
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
  ];

  async function getSearchDataTotal() {
    const searchParams = removeBlankFromObject(
      filterProps(formRef.current?.getFieldsFormatValue?.()),
    );
    const data = await repayRegistList({
      classification,
      secondaryClassification,
      ...searchParams,
    });
    return data?.total;
  }
  return (
    <div>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        rowKey="repayNo"
        search={{ labelWidth: 100, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        onSubmit={() => {
          if (!classification || !secondaryClassification) {
            // 只要有一个不存在 就不能发请求
            // 一级分类切换 由于联动 会清空二级分类
            message.error('产品一级和二级分类必填');
          }
        }}
        request={(params) => {
          if (!classification || !secondaryClassification) {
            // 只要有一个不存在 就不能发请求
            // 一级分类切换 由于联动 会清空二级分类
            message.error('产品一级和二级分类必填');
            return {} as any;
          }
          const channelStoreParams = getChannelStoreRequestParams(access);
          return repayRegistList(
            removeBlankFromObject(
              filterProps({
                ...params,
                ...channelStoreParams,
                classification,
                secondaryClassification,
              }),
            ),
          );
        }}
        toolBarRender={() => {
          return [
            access.hasAccess('repay_recording_export_postLoanMng_afterLoanList') && (
              <AsyncExport
                getSearchDataTotal={getSearchDataTotal}
                getSearchParams={() => {
                  //getFieldsFormatValue 是无法携带 ProTable 的 params
                  const params = formRef.current?.getFieldsFormatValue?.(true);
                  const channelStoreParams = getChannelStoreRequestParams(access);
                  return removeBlankFromObject(
                    filterProps({
                      ...channelStoreParams,
                      ...params,
                      classification,
                      secondaryClassification,
                    }),
                  );
                }}
                trigger={
                  <Button
                    type="primary"
                    onClick={(e) => {
                      if (!classification || !secondaryClassification) {
                        // 只要有一个不存在 就不能发请求
                        // 一级分类切换 由于联动 会清空二级分类
                        message.error('产品一级和二级分类必填');
                        e.stopPropagation();
                      }
                    }}
                  >
                    导出
                  </Button>
                }
                exportAsync={recordingExportAsync}
                taskCode={[
                  ItaskCodeEnValueEnum.REPAYMENT_RECORD,
                  ItaskCodeEnValueEnum.REPAYMENT_INSURANCE_RECORD,
                ]}
              />
            ),
          ];
        }}
        dateFormatter="string"
        columns={columns}
      />
    </div>
  );
};

export default RepayRegist;
