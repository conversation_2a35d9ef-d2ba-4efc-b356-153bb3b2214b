import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { history } from '@umijs/max';
import { Button } from 'antd';
import React, { memo, useRef } from 'react';
import { listColumns } from './constants/columns';
import type { IInsuranceOrderInfo, InsuranceOrderFilter } from './services';
import { exportInsuranceOrderList, getInsuranceOrderList } from './services';
const CarInsuranceAddSubtractList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  async function getSearchDataTotal() {
    const searchParams = filterProps(formRef.current?.getFieldsFormatValue?.());
    const data = await getInsuranceOrderList({
      ...searchParams,
    });
    return data?.total;
  }
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<IInsuranceOrderInfo, InsuranceOrderFilter>
          columns={listColumns}
          actionRef={actionRef}
          formRef={formRef}
          cardBordered
          search={{ defaultCollapsed: false, labelWidth: 140 }}
          request={async (params) => {
            const result = await getInsuranceOrderList(params);
            return {
              data: result.data,
              success: true,
              total: result.total,
            };
          }}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          toolBarRender={() => [
            <AsyncExport
              key="export"
              getSearchDataTotal={getSearchDataTotal}
              getSearchParams={() => {
                const params = formRef.current?.getFieldsFormatValue?.();
                return removeBlankFromObject(
                  filterProps({
                    ...params,
                  }),
                );
              }}
              trigger={<Button type="primary">导出</Button>}
              exportAsync={exportInsuranceOrderList}
              taskCode={[ItaskCodeEnValueEnum.INSURANCE_ADD_SUBTRACT]}
            />,
            <Button
              key="add"
              onClick={() => {
                history.push('/businessMng/car-insurance-add-subtract/detail');
              }}
              type="primary"
            >
              新建
            </Button>,
          ]}
        />
      </PageContainer>
    </>
  );
};
export default memo(CarInsuranceAddSubtractList);
