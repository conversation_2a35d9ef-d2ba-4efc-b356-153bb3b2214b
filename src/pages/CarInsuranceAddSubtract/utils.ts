import type { ModalFuncProps } from 'antd';
import { Modal } from 'antd';
import { insuranceStatusMap, insuranceTypeMap } from './constants/status';

const promiseModalConfirm = (props: ModalFuncProps) => {
  const { onOk, onCancel, ...rest } = props;
  return new Promise((r, j) => {
    Modal.confirm({
      ...rest,
      onCancel: () => {
        return Promise.resolve((onCancel ?? Promise.resolve.bind(Promise))?.()).then(j);
      },
      onOk: () => {
        return Promise.resolve((onOk ?? Promise.resolve.bind(Promise))?.()).then(r);
      },
    });
  });
};
// ['extendInfo', 'arrivalVoucher', 'unclaimedAmount'],
// ['extendInfo', 'arrivalVoucher', 'transactionTime'],
const createNestPath = (prefixPath: string[], childrenPath: string[]) => {
  return childrenPath.map((childPath) => {
    return [...prefixPath, childPath];
  });
};

const createPageTitle = (info) =>
  `${insuranceTypeMap[info.type]}-${insuranceStatusMap[info.status]}`;

const displayMoney = (value, prefix = '¥') => {
  return value === undefined || value === null || value === '' ? '-' : `${prefix}${value}`;
};
export { promiseModalConfirm, createNestPath, createPageTitle, displayMoney };
