// @import '~antd/lib/style/themes/default.less';

.ListCol {
  @bd: 1px solid @normal-color;
  &:nth-of-type(1),
  &:nth-of-type(2),
  &:nth-of-type(3) {
    .carListRow {
      border-top: @bd;
    }
  }
  .carListRow {
    border-bottom: @bd;
    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}

.draggerPictureCard {
  @halfMargin: (@margin-sm / 2);
  :global {
    .ant-upload-list {
      display: flex;
      flex-wrap: wrap;
      margin-right: -@halfMargin;
      margin-left: -@halfMargin;
    }
    .ant-upload-list-item-container {
      margin: @halfMargin;
    }
  }
}
.fileListContainer {
  display: flex;
  flex-wrap: wrap;
  .displayItem {
    margin: (@margin-sm / 2);
  }
}
.displayItem {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 55px;
  height: 55px;
  padding: @padding-xs;
  border: 1px solid @normal-color;
  border-radius: @border-radius-sm;
  .iconContainer {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @normal-color;
    font-size: @image-mask-font-size;
    inset: 0;

    & > span {
      &:not(:only-child):last-child {
        margin-left: (@margin-sm / 2);
      }

      opacity: 0;
    }

    &:hover {
      & > span {
        opacity: 1;
      }
    }
  }
}

.voucherPdfIcon {
  color: @primary-color;
  font-size: 40px;
}
.rejectReasonDetail {
  display: flex;
  color: @error-color;
  font-size: 14px;
  span:last-of-type {
    white-space: normal;
  }
}
