import HeaderTab from '@/components/HeaderTab';
import { downLoadExcel } from '@/utils/utils';
import { ProForm, useDeepCompareEffect } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Button, Form, Input, message, Space, Spin } from 'antd';
import { pick } from 'lodash';
import { bignumber, chain, format } from 'mathjs';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import AddSubtract from './components/addSubtract';
import LoanInfo from './components/loanInfo';
import OrderInfo from './components/orderInfo';
import VoucherInfo from './components/voucherInfo';
import { BtnType, orderStatusBtnMap } from './constants/orderBtn';
import { insuranceTypeMap } from './constants/status';
import styles from './index.less';
import type { IInsuranceOrderInfo, IPostData, ISubtractPremiumCar } from './services';
import {
  approvedInsuranceOrder,
  getInsuranceOrder,
  getReceipt,
  InvalidInsuranceOrder,
  OrderLendingRety,
  rejectedInsuranceOrder,
  resubmitInsuranceOrder,
  submitInsuranceOrder,
} from './services';
import { createNestPath, createPageTitle, promiseModalConfirm } from './utils';
type BtnT = typeof BtnType[keyof typeof BtnType];

function CarInsuranceAddSubtractEdit() {
  const businessNo = history.location.query.businessNo;
  const { data: businessInfo, run, mutate, loading } = useRequest(
    (businessNoVal) => {
      if (businessNoVal) {
        return getInsuranceOrder({ businessNo: businessNoVal as string });
      }
      return Promise.resolve({ data: null });
    },
    {
      defaultParams: [businessNo],
    },
  );
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [btnStatus, setBtnStatus] = useState<Partial<Record<BtnT, -1 | 0 | 1>>>(() =>
    Object.values(BtnType).reduce((res, cur) => {
      res[cur] = 0;
      return res;
    }, {}),
  );
  // 减保车辆信号 数据变更设置表单车辆
  const [currentCarList, setCurrentCarList] = useState<ISubtractPremiumCar[]>([]);
  const btnOperate = useRef<(btn: BtnT) => Promise<unknown>>();

  const getFormData = () => {
    return form.validateFields().then((values) => {
      const { extendInfo } = values;
      const { voucherUrlList = [], transactionNo = '' } = extendInfo?.arrivalVoucher || {};
      const unclaimedAmount = form.getFieldValue([
        'extendInfo',
        'arrivalVoucher',
        'unclaimedAmount',
      ]);
      const { subtractPremiumCarList = [] } = extendInfo?.subtractPremiumInfo || {};
      const postData: IPostData = {
        amount: values.amount,
        orderNo: values.orderNo,
        type: values.type,
        extendInfo: {
          arrivalVoucher: {
            voucherUrlList: voucherUrlList.map((voucher) => voucher.voucherUrl),
            transactionNo: transactionNo,
          },
        },
      };
      if (values.type == 2) {
        // 精度处理
        postData.amount = format(
          subtractPremiumCarList
            .reduce((res, cur) => {
              return res.add(bignumber(cur.amount || 0));
            }, chain(0))
            .done(),
        );
        postData.extendInfo.subtractPremiumInfo = {
          subtractPremiumCarList,
        };
      }
      const type = insuranceTypeMap[values.type];

      // 使用支付流水号校验
      if (transactionNo && unclaimedAmount) {
        if (+postData.amount > unclaimedAmount) {
          message.error(`【${type}金额${type == '减保' ? '合计' : ''}】超过银行流水号未认领金额`);
          return Promise.reject();
        }
      }
      // 合计值校验
      if (Number(postData.amount) <= 0) {
        message.error(`【${type}金额${type == '减保' ? '合计' : ''}】不能为0`);
        return Promise.reject();
      }
      return postData;
    });
  };
  const resetForm = () => {
    // 清除减保车辆
    setCurrentCarList([]);
    // 清除状态
    form.resetFields();
  };
  const reloadPage = (businessNo) => {
    // 清除状态
    resetForm();
    // 重新发起请求
    run(businessNo);
  };
  const gotoList = () => {
    history.push('/businessMng/car-insurance-add-subtract/list');
  };
  btnOperate.current = async (btn: BtnT) => {
    switch (btn) {
      // 数据修改
      case '提交':
        const postData = await getFormData();
        const { data } = await submitInsuranceOrder(postData);
        return reloadPage(data.businessNo);
      case '提交审核':
        const rePostData = await getFormData();
        return promiseModalConfirm({
          title: '是否提交审核',
          onOk: async () => {
            const { data } = await resubmitInsuranceOrder({
              businessNo: businessInfo!.businessNo,
              ...rePostData,
            });
            reloadPage(data.businessNo);
          },
        });
      // 流程流转
      case '审核通过':
        return promiseModalConfirm({
          title: '是否确认审核通过',
          onOk: async () => {
            await approvedInsuranceOrder({
              businessNo: businessInfo!.businessNo,
              type: businessInfo!.type,
            });
            gotoList();
          },
        });
      case '审核驳回':
        return promiseModalConfirm({
          title: '驳回原因',
          okType: 'danger',
          okText: '确认驳回',
          onCancel: () => {
            modalForm.resetFields();
          },
          onOk: async () => {
            const values = await modalForm.validateFields();
            await rejectedInsuranceOrder({
              ...values,
              businessNo: businessInfo!.businessNo,
              type: businessInfo!.type,
            });
            modalForm.resetFields();
            reloadPage(businessInfo!.businessNo);
          },
          content: (
            <Form form={modalForm}>
              <Form.Item name="remark" rules={[{ required: true, message: '驳回原因不能为空' }]}>
                <Input.TextArea autoSize={{ minRows: 5 }} maxLength={100} showCount />
              </Form.Item>
            </Form>
          ),
        });
      case '作废':
        return promiseModalConfirm({
          title: '是否确认作废',
          onOk: async () => {
            await InvalidInsuranceOrder({
              businessNo: businessInfo!.businessNo,
              type: businessInfo!.type,
            });
            reloadPage(businessInfo!.businessNo);
          },
        });
      case '放款重试':
        await OrderLendingRety({
          businessNo: businessInfo!.businessNo,
          type: businessInfo!.type,
        });
        return reloadPage(businessInfo!.businessNo);

      case '下载回单':
        const res = await getReceipt({ businessNo: businessInfo!.businessNo });
        return downLoadExcel(res, 'application/zip;charset=UTF-8');
      case '复用':
        resetForm();
        return mutate((pre) => {
          return pick(pre, [
            'orderNo',
            'userName',
            'channelName',
            'insuranceCompany',
            'insuranceSubject',
            'extendInfo',
            'lendingInfo',
            'type',
            'amount',
          ]) as IInsuranceOrderInfo;
        });

      case '关闭':
        return gotoList();
      default:
        return Promise.resolve();
    }
  };
  const setSafeBtnStatus = (btn: BtnT, status: 0 | 1) => {
    setBtnStatus((pre) => {
      return Object.keys(pre).reduce((res, cur) => {
        if (cur === btn) {
          res[cur] = status;
        } else {
          if (status == 0) {
            res[cur] = 0;
          } else {
            res[cur] = -1;
          }
        }
        return res;
      }, {});
    });
  };
  // 根据订单信息生成按钮
  const extraOperate = useMemo(() => {
    let orderStatusBtnMapValue: typeof orderStatusBtnMap[keyof typeof orderStatusBtnMap] =
      orderStatusBtnMap[businessInfo?.status || 1];
    if (typeof orderStatusBtnMapValue === 'function') {
      orderStatusBtnMapValue = orderStatusBtnMapValue(businessInfo?.type || 1);
    }
    return orderStatusBtnMapValue.map((btn) => {
      const status = btnStatus[btn];
      return (
        <Button
          type="primary"
          key={btn}
          disabled={status == -1}
          loading={status == 1}
          danger={['审核驳回'].includes(btn)}
          onClick={() => {
            setSafeBtnStatus(btn, 1);
            btnOperate.current?.(btn)?.finally(() => {
              setSafeBtnStatus(btn, 0);
            });
          }}
        >
          {btn}
        </Button>
      );
    });
  }, [btnStatus, businessInfo]);

  /**
   * 正常放到dependences中或者form.onValueChange中处理更好
   * 但onBlur依然会触发dependences、form.onValueChange，在拿不到变更前值情况下交互不符合预期
   */
  const handleOrderNoChange = () => {
    form.resetFields(
      createNestPath(
        ['extendInfo', 'arrivalVoucher'],
        [
          'transactionNo',
          'unclaimedAmount',
          'totalClaimAmount',
          'transactionTime',
          'remittanceAccountName',
          'remittanceAccountNumber',
        ],
      ),
    );
  };
  // 设置减保车辆
  useDeepCompareEffect(() => {
    const genFakeId = (item) => `${item.vin}@${item.plateNo}`;
    const oldCarAmount = (
      form.getFieldValue(['extendInfo', 'subtractPremiumInfo', 'subtractPremiumCarList']) || []
    ).reduce((res, cur) => {
      res[genFakeId(cur)] = cur.amount;
      return res;
    }, {});

    form.setFieldValue(
      ['extendInfo', 'subtractPremiumInfo', 'subtractPremiumCarList'],
      (currentCarList || []).map((item) => {
        const fakeId = genFakeId(item);
        if (fakeId in oldCarAmount) {
          return { ...item, amount: oldCarAmount[fakeId] };
        } else {
          return item;
        }
      }),
    );
  }, [currentCarList, form]);

  useEffect(() => {
    const initData = async () => {
      if (businessInfo) {
        const { extendInfo, status } = businessInfo;
        // 订单信息
        const { arrivalVoucher } = extendInfo || {};
        form.setFieldsValue({
          ...pick(businessInfo, [
            'userName',
            'channelName',
            'insuranceCompany',
            'insuranceSubject',
            'orderNo',
            'type',
            'amount',
            'lendingInfo',
          ]),

          extendInfo: {
            ...(extendInfo || {}),
            arrivalVoucher: {
              ...(arrivalVoucher || {}),
              voucherUrlList: arrivalVoucher?.voucherUrlList?.map((voucherUrl, i) => {
                return {
                  voucherUrl,
                  voucherNetWorkUrl: arrivalVoucher?.voucherNetWorkUrlList?.[i],
                };
              }),
            },
          },
        });
        // 审批驳回/复用拉取最新值
        if (status == 30 || !status) {
          form.validateFields([['extendInfo', 'arrivalVoucher', 'transactionNo'], 'orderNo']);
        }
      }
    };
    initData();
  }, [businessInfo, form]);

  // 新建 或者 驳回 可编辑
  const readonly = !(!businessInfo?.status || businessInfo?.status == 30);

  return (
    <>
      <HeaderTab />
      <PageContainer
        header={{
          title: (
            <div>
              {businessInfo?.status ? createPageTitle(businessInfo) : '新建加/减保'}
              {businessInfo?.extendInfo?.rejectReason && businessInfo?.status == 30 && (
                <div className={styles.rejectReasonDetail}>
                  <span>驳回原因：</span>
                  <span>{businessInfo?.extendInfo?.rejectReason}</span>
                </div>
              )}
            </div>
          ),
          breadcrumb: {},
          extra: extraOperate,
        }}
      >
        <Spin spinning={loading}>
          <ProForm
            readonly={readonly}
            layout="horizontal"
            submitter={false}
            form={form}
            initialValues={{
              orderNo: '',
              type: 1,
              amount: 0.01,
              extendInfo: {
                arrivalVoucher: {
                  voucherUrlList: [],
                  transactionNo: '',
                },
                subtractPremiumInfo: {
                  subtractPremiumCarList: [],
                },
              },
            }}
          >
            <Space direction="vertical" style={{ display: 'flex' }}>
              <OrderInfo
                setCurrentCarList={setCurrentCarList}
                handleOrderNoChange={handleOrderNoChange}
              />
              <VoucherInfo />
              <AddSubtract disbaleChangeType={businessInfo?.status == 30} />
              <LoanInfo />
            </Space>
          </ProForm>
        </Spin>
      </PageContainer>
    </>
  );
}

export default memo(CarInsuranceAddSubtractEdit);
