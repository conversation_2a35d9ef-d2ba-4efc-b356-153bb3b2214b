import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';

export type OrderType = {
  type: 1 | 2; // 1 加保 2 减保
};

//  到账凭证
export interface IArrivalVoucher {
  voucherUrlList: string[];
  voucherNetWorkUrlList: string[];
  transactionNo: string;
  totalClaimAmount: string;
  unclaimedAmount: string;
  transactionTime: Date;
  remittanceAccountName: string;
  remittanceAccountNumber: string;
}

// 减保车辆相关
export interface ISubtractPremiumCar {
  vin: string;
  plateNo: string;
  amount: string;
  commercialInsurancePremium?: string;
}

export type InsuranceOrderBaseInfo = Record<
  | 'businessNo'
  | 'orderNo'
  | 'channelCode'
  | 'channelName'
  | 'userNo'
  | 'userName'
  | 'insuranceSubject'
  | 'insuranceCompany'
  | 'amount',
  string
> &
  Record<'id' | 'status', number> &
  Record<'approvalPassTime' | 'lendingTime' | 'updatedAt' | 'createdAt', Date> &
  OrderType;

export interface LendingFlowNo {
  lendingNo: string;
  vin: string;
  amount: string;
  statusDesc: string;
}
export interface ILendingInfo {
  lendingAmount: string;
  lendingStartTime: Date;
  lendingInfoList: LendingFlowNo[];
  lendingMaster: string;
  receiptMaster: string;
  accountNo: string;
  lendingStatusDesc: string;
}

export interface IInsuranceOrderInfo extends InsuranceOrderBaseInfo {
  lendingInfo: ILendingInfo;
  extendInfo: {
    arrivalVoucher: IArrivalVoucher;
    subtractPremiumInfo: {
      subtractPremiumCarList: ISubtractPremiumCar[];
    };
    rejectReason: string;
  };
}

export interface IPostData extends OrderType {
  amount: string;
  orderNo: string;
  businessNo?: string;
  extendInfo: {
    arrivalVoucher: Pick<IArrivalVoucher, 'voucherUrlList' | 'transactionNo'>;
    subtractPremiumInfo?: {
      subtractPremiumCarList: ISubtractPremiumCar[];
    };
  };
}
/**
 * 约束对象中某几个字段是否同时出现
 * eg: type A = AllOrNoneWith<{a: string, b: string, c: string}, "a" | 'b'>
 * var a: A = {a: '1', b:'2', c: '3'} ✅
 * var c: A = {c: '3'} ✅
 * var n: A = {c: '3', a: '1'} ❌
 */
// type AllOrNoneWith<T, K extends keyof T> = ({ [P in K]: T[P] } | { [P in K]?: never }) & Omit<T, K>
type AllOrNone<T> = { [P in keyof T]: T[P] } | { [P in keyof T]?: never };

export type InsuranceOrderFilter = {
  current: number;
  pageSize?: number;
  statusList: string[];
  plateNo?: string;
  vin?: string;
} & Pick<
  InsuranceOrderBaseInfo,
  'orderNo' | 'channelName' | 'userName' | 'insuranceCompany' | 'insuranceSubject' | 'type'
> &
  AllOrNone<Record<'createdAtStart' | 'createdAtEnd', Date>> &
  AllOrNone<Record<'approvalPassTimeStart' | 'approvalPassTimeEnd', Date>> &
  AllOrNone<Record<'lendingTimeStart' | 'lendingTimeEnd', Date>>;

type Res<T> = {
  data: T;
};

// 分页查询加减保单
export const getInsuranceOrderList = (
  data: InsuranceOrderFilter,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<{ total: number; data: IInsuranceOrderInfo[] }> => {
  return request('/bizadmin/premium/adjust/query/page', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};
// 查询加减保单详情
export const getInsuranceOrder = (
  params: { businessNo: string },
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<IInsuranceOrderInfo>> => {
  return request('/bizadmin/premium/adjust/query/detail', {
    params,
    headers: bizadminHeader,
    ...options,
  });
};

// 导出加减保单
export const exportInsuranceOrderList = (
  data: InsuranceOrderFilter,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<Record<string, unknown>>> => {
  return request('/bizadmin/premium/adjust/export', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};

// 下载加保放款回单
export const getReceipt = (params: { businessNo: string }) => {
  return request('/bizadmin/premium/adjust/download/loan/receipt', {
    params,
    responseType: 'blob',
    getResponse: true,
    headers: bizadminHeader,
  });
};

// 查询支付流水信息
type GetPaymentFlowReq = {
  transactionNo: string;
  orderNo: string;
};
export const getPaymentFlow = (
  data: GetPaymentFlowReq,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<IArrivalVoucher>> => {
  return request('/bizadmin/premium/adjust/query/payment/flow', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  }).catch(() => {
    return { data: null };
  });
};

// 创建加减保单
export const submitInsuranceOrder = (
  data: IPostData,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<IInsuranceOrderInfo>> => {
  return request('/bizadmin/premium/adjust/add', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};

// 加减保单-重新提交审核
export const resubmitInsuranceOrder = (
  data: IPostData,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<IInsuranceOrderInfo>> => {
  return request('/bizadmin/premium/adjust/resubmit', {
    method: 'POST',
    headers: bizadminHeader,
    data,
    ...options,
  });
};

interface IFlow extends OrderType {
  businessNo: string;
  remark?: string;
}
// 加减保单审核通过
export const approvedInsuranceOrder = (
  data: IFlow,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<Record<string, unknown>>> => {
  return request('/bizadmin/premium/adjust/approval/pass', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};

// 加减保单审核驳回
export const rejectedInsuranceOrder = (
  data: IFlow,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<Record<string, string>>> => {
  return request('/bizadmin/premium/adjust/approval/reject', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};

// 加减保单作废
export const InvalidInsuranceOrder = (
  data: IFlow,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<Record<string, string>>> => {
  return request('/bizadmin/premium/adjust/invalidated', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};

export const OrderLendingRety = (
  data: IFlow,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<Res<Record<string, string>>> => {
  return request('/bizadmin/premium/adjust/retry/lending', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};

/**
 * 获取详情信息
 */
export function getCarInsuranceOrderDetail(
  orderNo: string,
  reuse?: true,
): Promise<
  Res<{
    orderStatus: number;
    channelType: number;
    enterpriseInfo: {
      enterpriseName: string;
    };
    personalInfo: {
      name: string;
    };
    baseInfo: {
      channelName: string;
    };
    insuranceInfo: {
      insuranceCompany: string;
      insuranceSubject: string;
    };
    carInfoDTOList: ISubtractPremiumCar[];
  }>
> {
  return request(`/bizadmin/insurance/policy/order/queryOrderDetail`, {
    method: 'GET',
    headers: bizadminHeader,
    params: {
      orderNo,
      reuse,
    },
  }).catch(() => {
    return { data: null };
  });
}
