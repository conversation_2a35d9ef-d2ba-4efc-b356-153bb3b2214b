import type { OrderType } from '../services';

export enum OrderStatus {
  CREATE = 1, // 新建
  PROCESSING = 10, // 处理中
  APPROVE = 20, // 审核
  APPROVE_REJECT = 30, // 审核驳回
  LOANING = 40, // 放款中/回款中
  LOAN_FAIL = 60, // 放款失败/回款失败
  LOAN_SUCCESS = 50, // 放款成功/回款成功
  INVALID = 70, // 作废
}

export enum BtnType {
  CLOSE = '关闭', // 关闭
  SUBMIT = '提交', // 提交
  APPROVE = '提交审核', // 审核
  APPROVE_PASS = '审核通过', // 审核通过
  APPROVE_REJECT = '审核驳回', // 审核拒绝
  LOAN_RETRY = '放款重试', // 放款重试
  DOWNLOAD = '下载回单', // 下载回单
  REUSE = '复用', // 复用
  INVALID = '作废', // 作废
}

const getSuccessBtn = (type: OrderType['type']) => {
  if (type === 1) {
    // 加保
    return [BtnType.CLOSE, BtnType.DOWNLOAD];
  } else if (type === 2) {
    // 减保
    return [BtnType.CLOSE];
  }
  return [];
};

export const orderStatusBtnMap = {
  [OrderStatus.CREATE]: [BtnType.CLOSE, BtnType.SUBMIT],
  [OrderStatus.PROCESSING]: [BtnType.CLOSE],
  [OrderStatus.APPROVE]: [BtnType.CLOSE, BtnType.APPROVE_PASS, BtnType.APPROVE_REJECT],
  [OrderStatus.APPROVE_REJECT]: [BtnType.CLOSE, BtnType.INVALID, BtnType.APPROVE],
  [OrderStatus.LOANING]: [BtnType.CLOSE],
  [OrderStatus.LOAN_FAIL]: [BtnType.CLOSE, BtnType.LOAN_RETRY],
  [OrderStatus.LOAN_SUCCESS]: getSuccessBtn,
  [OrderStatus.INVALID]: [BtnType.CLOSE, BtnType.REUSE],
};
