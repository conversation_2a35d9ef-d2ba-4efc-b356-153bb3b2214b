import type { FormInstance, ProColumns } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import type { IInsuranceOrderInfo } from '../services';
import { displayMoney } from '../utils';
import { insuranceStatusMap, insuranceTypeMap } from './status';

export const listColumns: ProColumns<IInsuranceOrderInfo>[] = [
  {
    dataIndex: 'orderNo',
    title: '订单号',
  },
  {
    title: '状态',
    dataIndex: 'statusList',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
    },
    valueEnum: insuranceStatusMap,
  },
  {
    title: '渠道名称',
    dataIndex: 'channelName',
  },
  {
    title: '类型',
    dataIndex: 'type',
    search: false,
    render(value) {
      return insuranceTypeMap[value as string];
    },
  },
  {
    title: '加/减保金额',
    dataIndex: 'amount',
    search: false,
    render(value) {
      return `¥${value}`;
    },
  },
  {
    title: '车辆信息',
    dataIndex: ['extendInfo', 'subtractPremiumInfo', 'subtractPremiumCarList'],
    search: false,
    render(value) {
      if (Array.isArray(value) && value.length > 0) {
        const title = value.map((item, i) => {
          const { plateNo, vin } = item;
          return (
            <pre key={i}>
              {plateNo} {vin}
            </pre>
          );
        });
        return (
          <Tooltip title={title} overlayInnerStyle={{ width: 'max-content' }}>
            <a>{value.length}辆</a>
          </Tooltip>
        );
      } else {
        return '-';
      }
    },
  },
  {
    title: '类型',
    dataIndex: 'type',
    valueType: 'select',
    hideInTable: true,
    valueEnum: insuranceTypeMap,
  },
  {
    title: '客户名称',
    dataIndex: 'userName',
  },

  {
    title: '保司名称',
    dataIndex: 'insuranceCompany',
  },
  {
    title: '投保主体',
    dataIndex: 'insuranceSubject',
    valueType: 'select',
    valueEnum: {
      广州货满满汽车咨询有限公司: '广州货满满汽车咨询有限公司',
      '易立信区块链科技(广州)有限公司': '易立信区块链科技(广州)有限公司',
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    search: false,
  },
  {
    title: '审核通过日期',
    dataIndex: 'approvalPassTime',
    search: false,
  },
  {
    title: '放款成功日期',
    dataIndex: 'lendingTime',
    search: false,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    valueType: 'dateRange',
    initialValue: [dayjs().subtract(1, 'year'), dayjs()],
    hideInTable: true,
    fieldProps: {
      allowClear: false,
    },
    search: {
      transform: (value) => {
        return {
          createdAtStart: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          createdAtEnd: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        };
      },
    },
  },

  {
    title: '审核通过日期',
    dataIndex: 'approvalPassTime',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value) => {
        return {
          approvalPassTimeStart: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          approvalPassTimeEnd: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        };
      },
    },
  },

  {
    title: '放款成功日期',
    dataIndex: 'lendingTime',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value) => {
        return {
          lendingTimeStart: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          lendingTimeEnd: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        };
      },
    },
  },

  {
    title: '车牌号/新车合格证号',
    dataIndex: 'plateNo',
    hideInTable: true,
    dependencies: ['type'],
    renderFormItem: (item, { defaultRender }, form: FormInstance) => {
      return form.getFieldValue('type') == 2 ? defaultRender(item) : false;
    },
  },
  {
    title: '车架号',
    hideInTable: true,
    dataIndex: 'vin',
    dependencies: ['type'],
    renderFormItem: (item, { defaultRender }, form: FormInstance) => {
      return form.getFieldValue('type') == 2 ? defaultRender(item) : null;
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    fixed: 'right',
    search: false,
    width: '100px',
    render(value) {
      return insuranceStatusMap[value as string];
    },
  },
  {
    title: '操作',
    valueType: 'option',
    key: 'option',
    fixed: 'right',
    width: '100px',
    render: (text, record) => {
      return [
        <Button
          key="editable"
          variant="link"
          type="link"
          onClick={() => {
            history.push(
              `/businessMng/car-insurance-add-subtract/detail?businessNo=${record.businessNo}`,
            );
          }}
        >
          查看详情
        </Button>,
      ];
    },
  },
];

export const loanColumns = [
  {
    title: '放款流水号',
    dataIndex: 'lendingNo',
    key: 'lendingNo',
  },
  {
    title: '车架号',
    dataIndex: 'vin',
    key: 'vin',
  },
  {
    title: '放款金额',
    dataIndex: 'amount',
    key: 'amount',
    render: (text) => {
      return displayMoney(text);
    },
  },
  {
    title: '放款状态',
    dataIndex: 'statusDesc',
    key: 'statusDesc',
  },
];
