import { ProCard, ProFormDependency, ProFormDigit, ProFormRadio } from '@ant-design/pro-components';
import { Col, Descriptions, Row } from 'antd';
import { bignumber, chain, format } from 'mathjs';
import React from 'react';
import { displayMoney } from '../utils';
import CarList from './carList';
interface AddSubtractProps {
  disbaleChangeType: boolean;
}
// const CarList = React.lazy(() => import('./carList'));
export default ({ disbaleChangeType }: AddSubtractProps) => {
  return (
    <ProCard title="加/减保信息">
      <Row>
        <Col>
          <ProFormRadio.Group
            disabled={disbaleChangeType}
            name="type"
            label="类型"
            rules={[{ required: true, message: '类型不能为空' }]}
            options={[
              {
                label: '加保',
                value: 1,
              },
              {
                label: '减保',
                value: 2,
              },
            ]}
          />
        </Col>
      </Row>
      <ProFormDependency
        name={[['extendInfo', 'subtractPremiumInfo', 'subtractPremiumCarList'], 'type']}
      >
        {({ extendInfo, type }) => {
          const subtractPremiumCarList =
            extendInfo?.subtractPremiumInfo?.subtractPremiumCarList || [];
          let totalCount = 0;
          // 精度处理
          const totalAmount = format(
            (subtractPremiumCarList || [])
              .reduce((ch, cur) => {
                const amount = Number(cur.amount || 0);
                if (amount > 0) {
                  totalCount++;
                }
                return ch.add(bignumber(amount));
              }, chain(0))
              .done(),
          );

          return type == 1 ? (
            <Row>
              <ProFormDigit
                label="加保金额"
                name="amount"
                rules={[{ required: true, message: '加保金额不能为空' }]}
                fieldProps={{ precision: 2, prefix: '¥' }}
                style={{ width: '100%' }}
                min={0.01}
                {...{
                  render: (value, props) => {
                    return displayMoney(value, props.prefix);
                  },
                }}
                max={99999.99}
              />
            </Row>
          ) : (
            <>
              <Row>
                <Col span={12}>
                  <Descriptions
                    column={{ lg: 2, md: 1, sm: 1, xs: 1 }}
                    title={<span style={{ fontWeight: 400 }}>减保信息</span>}
                  >
                    <Descriptions.Item label="减保金额合计" span={1}>
                      {displayMoney(Number(totalAmount).toFixed(2))}
                    </Descriptions.Item>
                    <Descriptions.Item label="减保车辆数" span={1}>
                      {totalCount}辆
                    </Descriptions.Item>
                  </Descriptions>
                </Col>
              </Row>

              {/* <Suspense fallback={<div>Loading...</div>}>
                <CarList />
              </Suspense> */}
              <CarList />
            </>
          );
        }}
      </ProFormDependency>
    </ProCard>
  );
};
