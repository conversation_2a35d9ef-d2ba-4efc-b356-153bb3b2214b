import { ProFormDigit, ProFormText } from '@ant-design/pro-components';
import { Col, Form, Row } from 'antd';
import React from 'react';
import styles from '../index.less';
import { displayMoney } from '../utils';

const CarList = () => {
  return (
    <Row gutter={32}>
      <Form.List name={['extendInfo', 'subtractPremiumInfo', 'subtractPremiumCarList']}>
        {(fields) =>
          fields.map((field) => {
            return (
              <Col span={8} key={field.key} className={styles.ListCol}>
                <Row className={styles.carListRow} key={field.key} align="middle">
                  <Col span={12}>
                    {/* <ProFormText
                                    hidden
                                    readonly
                                    name={[field.name, 'commercialInsurancePremium']}
                                  /> */}
                    <ProFormText readonly name={[field.name, 'plateNo']} />
                    <ProFormText readonly name={[field.name, 'vin']} />
                  </Col>
                  <Col span={12} className={styles.amount}>
                    <ProFormDigit
                      name={[field.name, 'amount']}
                      rules={[
                        { required: true, message: '金额不能为空' },
                        // ({ getFieldValue }) => ({
                        //   async validator(_, value) {
                        //     if (!value) {
                        //       return Promise.resolve();
                        //     }
                        //     const commercialInsurancePremium = getFieldValue([
                        //       'subtractPremiumCarList',
                        //       field.name,
                        //       'commercialInsurancePremium',
                        //     ]);

                        //     if (value > commercialInsurancePremium) {
                        //       return Promise.reject(
                        //         new Error('减保金额超过商业险金额'),
                        //       );
                        //     }

                        //     return Promise.resolve();
                        //   },
                        // }),
                      ]}
                      fieldProps={{ precision: 2, prefix: '¥' }}
                      min={0}
                      max={99999.99}
                      {...{
                        render: (value, props) => {
                          return displayMoney(value, props.prefix);
                        },
                      }}
                    />
                  </Col>
                </Row>
              </Col>
            );
          })
        }
      </Form.List>
    </Row>
  );
};

export default CarList;
