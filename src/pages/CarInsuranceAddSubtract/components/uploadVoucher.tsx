import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import { getBizadminUploadAction } from '@/pages/BusinessExcel/utils';
import { bizadminHeader } from '@/services/consts';
import { getAuthHeaders } from '@/utils/auth';
import { getUuid } from '@/utils/utils';
import {
  DeleteOutlined,
  EyeOutlined,
  FilePdfOutlined,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Image, message, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadFile, UploadFileStatus, UploadProps } from 'antd/es/upload/interface';
import React, { useMemo, useRef, useState } from 'react';
import styles from '../index.less';
const { Dragger } = Upload;

type Voucher = {
  voucherUrl: string;
  voucherNetWorkUrl: string;
  uid?: string;
  ret?: number;
  status?: UploadFileStatus;
};
type Props = {
  value?: Voucher[];
  onChange?: (val: Voucher[]) => void;
  readonly?: boolean;
};
const UploadImage: React.FC<Props> = (props) => {
  const { onChange, value, readonly } = props;

  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState({ visible: false, url: '' });
  const previewRef = useRef<ImagePreviewInstance>();
  const fileList = useMemo(() => {
    return value?.map((item) => {
      return {
        status: item.status || 'done',
        uid: item?.uid || getUuid(),
        name: item.voucherUrl,
        thumbUrl: item.voucherNetWorkUrl,
        url: item.voucherNetWorkUrl,
        response: {
          ret: item.ret || 0,
          data: {
            filePath: item.voucherUrl,
            netWorkPath: item.voucherNetWorkUrl,
            fileName: item.voucherUrl,
          },
        },
      };
    });
  }, [value]);
  const handleChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
    const fileList = info.fileList.map((file) => {
      const { data = {}, ret } = file.response || {};
      const { netWorkPath, filePath } = data;
      return {
        ret,
        uid: file.uid,
        status: file.status,
        voucherUrl: filePath,
        voucherNetWorkUrl: netWorkPath,
      };
    });
    onChange?.(fileList);
    if (info.fileList.some((item) => item.status === 'uploading')) {
      setLoading(true);
    } else if (info.fileList.every((item) => item.status !== 'uploading')) {
      setLoading(false);
    }
    if (info.file.status === 'done') {
      const { ret } = info.file.response || {};
      if (ret === 0) {
        message.success('上传成功');
      } else {
        message.error('上传有误');
      }
      onChange?.(
        fileList.filter((item) => {
          if (item.uid === info.file.uid && item.ret !== 0) {
            return false;
          } else {
            return true;
          }
        }),
      );
    }
  };

  function beforeUpload(file: RcFile) {
    const format = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (!format.includes(file?.type)) {
      message.error('格式为仅支持.pdf,.png,.jpg,.jpeg,.webp');
      return false || Upload.LIST_IGNORE;
    } else if (file?.size > 5 * 1024 * 1024) {
      message.error('图片需小于5M');
      return false || Upload.LIST_IGNORE;
    } else {
      return true;
    }
  }
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>点击或拖拽上传</div>
    </div>
  );
  const handlePreview = (file) => {
    const { url } = file;

    const isPdf = url?.includes('.pdf');
    if (isPdf) {
      previewRef.current?.previewFile({
        url: url,
      });
    } else {
      setShowPreview({ visible: true, url });
    }
  };
  const renderItem = (originNode, file, fileList, actions) => {
    const { url } = file;
    const isPdf = url?.includes('.pdf');

    return (
      <div className={styles.displayItem}>
        {isPdf ? (
          <FilePdfOutlined className={styles.voucherPdfIcon} />
        ) : (
          <img src={url} width="100%" />
        )}
        <div className={styles.iconContainer}>
          <EyeOutlined onClick={actions.preview} />
          {readonly ? null : <DeleteOutlined onClick={actions.remove} />}
        </div>
      </div>
    );
  };

  return (
    <>
      {readonly ? (
        <div className={styles.fileListContainer}>
          {fileList?.map((file) => {
            return renderItem(undefined, file, fileList, { preview: () => handlePreview(file) });
          })}
        </div>
      ) : (
        <Dragger
          multiple={true}
          disabled={readonly}
          className={styles.draggerPictureCard}
          fileList={fileList}
          action={getBizadminUploadAction()}
          headers={{ ...getAuthHeaders(), ...bizadminHeader }}
          name="file"
          accept=".pdf,.png,.jpg,.jpeg,.webp"
          data={{
            destPath: 'CAR_POLICY_PREMIUM_ADJUST',
            acl: 'PUBLIC_READ',
            attachment: false,
          }} // 后端商量默认格式
          beforeUpload={beforeUpload}
          onChange={handleChange}
          onPreview={handlePreview}
          itemRender={renderItem}
        >
          {uploadButton}
        </Dragger>
      )}

      <ImagePreview ref={previewRef as any} />
      <Image
        width={200}
        style={{ display: 'none' }}
        src={showPreview.url}
        preview={{
          visible: showPreview.visible,
          src: showPreview.url,
          onVisibleChange: (visible) => {
            setShowPreview({ visible, url: '' });
          },
        }}
      />
    </>
  );
};

export default UploadImage;
