import { InsuranceSubjectMap } from '@/pages/CarInsurancePolicyManage/enum';
import { ProCard, ProFormText } from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import React, { useState } from 'react';
import type { ISubtractPremiumCar } from '../services';
import { getCarInsuranceOrderDetail } from '../services';

interface OrderInfoProps {
  setCurrentCarList: React.Dispatch<React.SetStateAction<ISubtractPremiumCar[]>>;
  handleOrderNoChange: () => void;
}
export default ({ handleOrderNoChange, setCurrentCarList }: OrderInfoProps) => {
  const [validating, setValidating] = useState(false);
  return (
    <ProCard title="订单信息">
      <Row gutter={16}>
        <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
          <ProFormText
            label="订单号"
            name="orderNo"
            hasFeedback
            validateStatus={validating ? 'validating' : ''}
            validateTrigger="onBlur"
            fieldProps={{ onChange: handleOrderNoChange }}
            rules={[
              { required: true, message: '订单号不能为空' },
              ({ resetFields, setFieldsValue }) => ({
                async validator(_, value) {
                  const resetInfo = () => {
                    setCurrentCarList([]);
                    resetFields([
                      'userName',
                      'channelName',
                      'insuranceCompany',
                      'insuranceSubject',
                    ]);
                  };

                  if (!value) {
                    resetInfo();
                    return Promise.resolve();
                  }
                  setValidating(true);
                  const { data: carInsuranceDetail } = await getCarInsuranceOrderDetail(value);
                  setValidating(false);

                  if (!carInsuranceDetail) {
                    resetInfo();
                    return Promise.reject(new Error('没有找到此订单'));
                  } else if (
                    carInsuranceDetail.orderStatus !== 51 &&
                    carInsuranceDetail.orderStatus !== 60
                  ) {
                    resetInfo();
                    return Promise.reject(new Error('该订单不符合加/减保条件'));
                  }
                  const {
                    channelType,
                    enterpriseInfo,
                    personalInfo,
                    baseInfo,
                    insuranceInfo,
                    carInfoDTOList,
                  } = carInsuranceDetail;
                  setFieldsValue({
                    userName: channelType === 5 ? enterpriseInfo.enterpriseName : personalInfo.name,
                    channelName: baseInfo.channelName,
                    insuranceCompany: insuranceInfo.insuranceCompany,
                    insuranceSubject: InsuranceSubjectMap[insuranceInfo.insuranceSubject],
                  });
                  setCurrentCarList(
                    carInfoDTOList?.map((car) => {
                      return {
                        plateNo: car.plateNo,
                        vin: car.vin,
                        amount: car.amount || '0',
                        // commercialInsurancePremium: car.commercialInsurancePremium || 0,
                      };
                    }),
                  );

                  return Promise.resolve();
                },
              }),
            ]}
          />
        </Col>
        <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
          <ProFormText label="客户名称" name="userName" readonly />
        </Col>
        <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
          <ProFormText label="渠道名称" name="channelName" readonly />
        </Col>
        <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
          <ProFormText label="保司名称" name="insuranceCompany" readonly />
        </Col>
        <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
          <ProFormText label="投保主体" name="insuranceSubject" readonly />
        </Col>
      </Row>
    </ProCard>
  );
};
