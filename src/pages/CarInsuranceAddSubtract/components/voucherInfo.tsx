import {
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormText,
} from '@ant-design/pro-components';
import { Col, Form, Row, theme } from 'antd';
import React, { useContext, useState } from 'react';
import { getPaymentFlow } from '../services';
import { createNestPath, displayMoney } from '../utils';
import UploadVoucher from './uploadVoucher';
const { useToken } = theme;
export default () => {
  const { mode } = useContext(ProForm.EditOrReadOnlyContext);
  const { token } = useToken();
  const [validating, setValidating] = useState(false);
  const readonly = mode == 'read';
  return (
    <ProCard title="到账凭证">
      <Row gutter={16}>
        <Col xs={14} sm={12} md={10} lg={8} xl={7}>
          <ProForm.Item
            label="凭证"
            name={['extendInfo', 'arrivalVoucher', 'voucherUrlList']}
            rules={[{ required: true, message: '到账凭证不能为空' }]}
          >
            <UploadVoucher readonly={readonly} />
          </ProForm.Item>
        </Col>
        <Form.Item shouldUpdate noStyle>
          {({ getFieldValue }) => {
            return (
              !(readonly && !getFieldValue(['extendInfo', 'arrivalVoucher', 'transactionNo'])) && (
                <Col xs={10} sm={12} md={14} lg={16} xl={17}>
                  <Row gutter={16}>
                    <Col sm={24} md={12} lg={12} xl={8}>
                      <ProFormDependency name={['orderNo']}>
                        {({ orderNo }) => {
                          return (
                            <ProFormText
                              hasFeedback
                              validateStatus={validating ? 'validating' : ''}
                              disabled={!orderNo}
                              label="银行流水号"
                              name={['extendInfo', 'arrivalVoucher', 'transactionNo']}
                              validateTrigger="onBlur"
                              rules={[
                                ({ getFieldValue, resetFields, setFieldValue }) => ({
                                  async validator(_, value) {
                                    const resetInfo = () =>
                                      resetFields(
                                        createNestPath(
                                          ['extendInfo', 'arrivalVoucher'],
                                          [
                                            'unclaimedAmount',
                                            'totalClaimAmount',
                                            'transactionTime',
                                            'remittanceAccountName',
                                            'remittanceAccountNumber',
                                          ],
                                        ),
                                      );
                                    if (!value) {
                                      resetInfo();
                                      return Promise.resolve();
                                    }
                                    setValidating(true);
                                    const flowDetail = await getPaymentFlow({
                                      transactionNo: value,
                                      orderNo,
                                    });
                                    setValidating(false);
                                    if (!flowDetail.data) {
                                      resetInfo();
                                      return Promise.reject(new Error('该流水号不存在'));
                                    }
                                    setFieldValue(['extendInfo', 'arrivalVoucher'], {
                                      ...(getFieldValue(['extendInfo', 'arrivalVoucher']) || {}),
                                      ...flowDetail.data,
                                    });
                                    return Promise.resolve();
                                  },
                                }),
                              ]}
                            />
                          );
                        }}
                      </ProFormDependency>
                    </Col>
                    <Col sm={24} md={12} lg={12} xl={8}>
                      <Row>
                        <ProFormDigit
                          label="到账金额"
                          name={['extendInfo', 'arrivalVoucher', 'totalClaimAmount']}
                          readonly
                          fieldProps={{ precision: 2, prefix: '¥' }}
                          {...{
                            render: (value, props) => {
                              return displayMoney(value, props.prefix);
                            },
                          }}
                        />
                        <Form.Item shouldUpdate>
                          {({ getFieldValue }) => {
                            const value = getFieldValue([
                              'extendInfo',
                              'arrivalVoucher',
                              'unclaimedAmount',
                            ]);
                            return (
                              !readonly && (
                                <>
                                  &nbsp;
                                  <span style={{ color: token.colorPrimaryText }}>
                                    (未认领金额：
                                    {displayMoney(value)})
                                  </span>
                                </>
                              )
                            );
                          }}
                        </Form.Item>
                      </Row>
                    </Col>
                    <Col sm={24} md={10} lg={12} xl={8}>
                      <ProFormText
                        label="到账时间"
                        name={['extendInfo', 'arrivalVoucher', 'transactionTime']}
                        readonly
                      />
                    </Col>
                    <Col sm={24} md={12} lg={12} xl={8}>
                      <ProFormText
                        label="汇款账户名"
                        name={['extendInfo', 'arrivalVoucher', 'remittanceAccountName']}
                        readonly
                      />
                    </Col>
                    <Col sm={24} md={12} lg={12} xl={8}>
                      <ProFormText
                        label="汇款账号"
                        name={['extendInfo', 'arrivalVoucher', 'remittanceAccountNumber']}
                        readonly
                      />
                    </Col>
                  </Row>
                </Col>
              )
            );
          }}
        </Form.Item>
      </Row>
    </ProCard>
  );
};
