import { ProCard, ProFormDigit, ProFormText } from '@ant-design/pro-components';
import { Col, Drawer, Form, Row, Table, theme } from 'antd';
import { isEqual } from 'lodash';
import type { FunctionComponent } from 'react';
import React, { useState } from 'react';
import { loanColumns } from '../constants/columns';
import type { LendingFlowNo } from '../services';
import { displayMoney } from '../utils';
const { useToken } = theme;

const LoanInfo: FunctionComponent = () => {
  const { token } = useToken();
  const [open, setOpen] = useState(false);
  const renderMoreFlowNo = (list, type) => {
    if (Array.isArray(list) && list.length > 0) {
      const displayNo = list[0].lendingNo;
      if (list.length > 1) {
        return (
          <>
            <span>
              {displayNo}...
              <span
                onClick={() => setOpen(true)}
                style={{ color: token.colorPrimaryText, cursor: 'pointer' }}
              >
                查看全部
              </span>
            </span>
            <Drawer
              size="large"
              title="放款流水号"
              closable={{ 'aria-label': 'Close Button' }}
              onClose={() => setOpen(false)}
              open={open}
            >
              <Table<LendingFlowNo>
                columns={loanColumns.filter((item) => {
                  if (type == 1 && item.key == 'vin') {
                    return false;
                  }
                  return true;
                })}
                dataSource={list}
              />
            </Drawer>
          </>
        );
      } else {
        return displayNo;
      }
    } else {
      return '-';
    }
  };
  return (
    <>
      <Form.Item
        shouldUpdate={(prevValues, curValues) =>
          !isEqual(prevValues.lendingInfo, curValues.lendingInfo)
        }
      >
        {({ getFieldValue }) => {
          const type = getFieldValue('type');
          return (
            getFieldValue('lendingInfo') && (
              <ProCard title="放款信息">
                <Row gutter={16}>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <ProFormDigit
                      label="放款金额"
                      name={['lendingInfo', 'lendingAmount']}
                      readonly
                      fieldProps={{ precision: 2, prefix: '¥' }}
                      {...{
                        render: (value, props) => {
                          return displayMoney(value, props.prefix);
                        },
                      }}
                    />
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <ProFormText
                      label="放款发起时间"
                      name={['lendingInfo', 'lendingStartTime']}
                      readonly
                    />
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <Form.Item shouldUpdate label="放款流水号">
                      {({ getFieldValue }) => {
                        const list = getFieldValue(['lendingInfo', 'lendingInfoList']);
                        return renderMoreFlowNo(list, type);
                      }}
                    </Form.Item>
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <ProFormText
                      label="放款主体"
                      name={['lendingInfo', 'lendingMaster']}
                      readonly
                    />
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <ProFormText
                      label="收款主体"
                      name={['lendingInfo', 'receiptMaster']}
                      readonly
                    />
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <ProFormText label="收款账号" name={['lendingInfo', 'accountNo']} readonly />
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <ProFormText
                      label="放款状态"
                      name={['lendingInfo', 'lendingStatusDesc']}
                      readonly
                    />
                  </Col>
                  <Col sm={12} md={12} lg={12} xl={8} xxl={6}>
                    <Form.Item shouldUpdate noStyle>
                      {({ getFieldValue }) => {
                        const lendingSuccessTime = getFieldValue([
                          'lendingInfo',
                          'lendingSuccessTime',
                        ]);
                        return lendingSuccessTime ? (
                          <ProFormText
                            label="放款成功时间"
                            name={['lendingInfo', 'lendingSuccessTime']}
                            readonly
                          />
                        ) : null;
                      }}
                    </Form.Item>
                  </Col>
                </Row>
              </ProCard>
            )
          );
        }}
      </Form.Item>
    </>
  );
};

export default LoanInfo;
