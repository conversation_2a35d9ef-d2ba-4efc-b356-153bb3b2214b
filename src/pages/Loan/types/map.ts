/**
 * 放款状态
 */
export const loanStatusMap = {
  0: '放款失败',
  1: '待放款',
  2: '放款成功',
  '-1': '已取消',
  3: '待请款',
  10: '待一审',
  11: '待二审',
  12: '驳回',
  14: '待审核',
};

export type UloanStatus = keyof typeof loanStatusMap;

/**
 * 放款类型
 */
export const loanTypeMap = {
  1: '进件',
  2: '债转',
  3: '代偿',
  5: '金融放款',
  6: '委托投保',
  10: '委托加保',
  11: '减保回款',
  7: '退保回款',
  8: '退保退款',
};

/**
 * 放款方式
 */
export const loanModelMap = {
  1: '线上',
  2: '线下',
};
