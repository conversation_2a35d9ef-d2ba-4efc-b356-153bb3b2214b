/*
 * @Author: your name
 * @Date: 2021-04-19 16:44:20
 * @LastEditTime: 2024-05-15 16:40:28
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/components/AddLoanResourceModal.tsx
 */
import { LOAN_MANAGE_CODE, NOTARIZATION_STATUS, NOTARIZATION_STATUS_MAP } from '@/enums';
import { isChannelStoreUser } from '@/utils/utils';
import { ProForm } from '@ant-design/pro-components';
import { ProFormDatePicker, ProFormTextArea } from '@ant-design/pro-form';
import { history, useAccess } from '@umijs/max';
import { Button, Col, Descriptions, Form, message, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { addApplyData, getGpsDeviceStatus } from '../service';
import type { IleaseDetail, IocrItem } from '../types';
import type { UloanStatus } from '../types/map';
import LoanProFormUploadDragger, { channelUseAuthMap } from './LoanProFormUploadDragger';

export type AddLoanResourceModalProps = {
  userNo: string;
  refresh: () => void;
  data: IleaseDetail;
  status: UloanStatus;
  orderDetail: any;
  noGuaranteeTypes: boolean;
};

enum GPS_STATUS {
  NOT_USE = -1,
  OFF_LINE = 1,
  STATIC = 2,
  RUNING = 3,
}

const gpsStatusMap = {
  '-1': '未启用',
  1: '离线',
  2: '静止',
  3: '行驶中',
};

enum LICENSE_TYPE {
  COMPANY = 1, // 挂靠
  PERSONAL = 2, // 个户
}

const LoanResource: React.FC<AddLoanResourceModalProps> = (props) => {
  const { lendingNo, orderNo }: any = history.location.query;
  const { userNo, refresh, data, orderDetail, status, noGuaranteeTypes, vin }: any = props;
  const { carPurchaseInvoiceOcr = null, trafficInsuranceOcr = null, businessInsuranceOcr = null } =
    data || {};
  const [form] = Form.useForm();
  const [gpsStatusList, setGpsStatusList] = useState([]);
  const access = useAccess();

  const isLeaseChannelUser = isChannelStoreUser(access);

  const getGpsStatusList = async (vin: string) => {
    const res = await getGpsDeviceStatus(vin);
    setGpsStatusList(res?.data?.positions || []);
  };

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data]);

  useEffect(() => {
    getGpsStatusList(vin);
  }, [vin]);

  function checkOcr(ocrValueList: IocrItem[] = []) {
    if (!ocrValueList?.length) {
      return false;
    }

    for (let i = 0; i < ocrValueList.length; i++) {
      const ocr = ocrValueList[0];
      if (!ocr.result && ocr.fieldName === '车架号') {
        // 只要有一个不通过就不允许提交 // 只校验车架号
        return false;
      }
    }
    // 走到这里说明都通过了
    return true;
  }

  const getDisabled = () => {
    if (isLeaseChannelUser) {
      if (!channelUseAuthMap.save.includes(status)) {
        return true;
      }
      return false;
    }
    return false;
  };

  async function applyData(operateType: 'save' | 'submit', ocr: Record<string, string> = {}) {
    if (isLeaseChannelUser) {
      if (operateType === 'save') {
        if (!channelUseAuthMap.save.includes(status)) {
          return;
        }
      }
      if (operateType === 'submit') {
        if (!channelUseAuthMap.submit.includes(status)) {
          message.error('无提交权限');
          return;
        }
      }
    }
    // 处理ocr识别出来的开票日期
    if (ocr?.carPurchaseInvoiceOcr) {
      const carPurchaseInvoiceOcrObj = JSON.parse(ocr.carPurchaseInvoiceOcr) || {};
      const { ocrValueList } = carPurchaseInvoiceOcrObj;
      const invoiceDate = ocrValueList?.find((item) => item.fieldName === '开票日期')?.fieldValue;
      if (invoiceDate) {
        form.setFieldValue('invoiceDate', invoiceDate);
      }
    }

    if (operateType === 'submit' && isLeaseChannelUser) {
      // 提交请款 且是 渠道用户的时候 校验ocr 识别失败不能提交
      const carPurchaseInvoiceOcrObj = JSON.parse(carPurchaseInvoiceOcr) || {};
      const trafficInsuranceOcrObj = JSON.parse(trafficInsuranceOcr) || {};
      const businessInsuranceOcrObj = JSON.parse(businessInsuranceOcr) || {};

      const hasError = [
        checkOcr(carPurchaseInvoiceOcrObj.ocrValueList),
        checkOcr(trafficInsuranceOcrObj.ocrValueList),
        checkOcr(businessInsuranceOcrObj.ocrValueList),
      ].includes(false);
      if (hasError) {
        message.error('有OCR没有识别或者不通过,请核实');
        return;
      }
    }
    const values = form.getFieldsValue();

    // 转换 upload value值
    const fileNameList = [
      'carPurchaseInvoice',
      'trafficInsurance',
      'businessInsurance',
      'purchaseTaxReceipt',
      'drivingLicense',
      'carRegistration',
      'gpsSysScreenshot',
      'guaranteeLetter',
      'otherEnclosure',
    ];

    for (let i = 0; i < fileNameList.length; i++) {
      const name = fileNameList[i];
      const value = values[name];
      const newValue: any = [];
      for (let j = 0; j < value?.length; j++) {
        const file = value[j];
        const { filePath, url, uid, name } = file || {};
        if (!filePath) {
          // 只要有一个不存在就 不能保存
          // 可能存在多个 表单同时上传 一个已经上传成功 另一个还在上传中 这个提示会有点问题
          // message.error('filePath不存在');
          return;
        } else {
          newValue.push({
            filePath,
            url,
            uid,
            name,
          });
        }
      }
      values[name] = newValue;
    }

    const invoiceDate = values.invoiceDate ? dayjs(values.invoiceDate).format('YYYY-MM-DD') : '';
    await addApplyData({
      ...values,
      invoiceDate,
      orderNo,
      lendingNo,
      userNo,
      operateType,
      carPurchaseInvoiceOcr,
      trafficInsuranceOcr,
      businessInsuranceOcr,
      ...ocr,
    });
    // 添加之后 状态也可能会变 重新获取 数据
    if (operateType === 'submit' || ocr) {
      refresh();
    }
  }

  // 保存草稿 操作不需要刷新数据
  // 提交操作是需要刷新数据

  function renderSubmitJsx() {
    const jsx = (
      <Button
        type="primary"
        style={{ marginLeft: 20, marginBottom: 20 }}
        onClick={() => {
          form.submit();
        }}
      >
        提交
      </Button>
    );
    if (isLeaseChannelUser) {
      if (channelUseAuthMap.submit.includes(status)) {
        return jsx;
      }
      return null;
    }
    // 挂起不展示提交按钮
    if ([LOAN_MANAGE_CODE.HANG_UP].includes(status)) {
      return null;
    }
    return jsx;
  }

  return (
    <>
      <>{renderSubmitJsx()}</>
      <ProForm
        layout="horizontal"
        form={form}
        scrollToFirstError // 不知道什么不起作用
        disabled={getDisabled()}
        onFinish={async () => {
          applyData('submit');
        }}
        onValuesChange={(changeValues) => {
          console.log('changeValues', changeValues);
          // 这里触发自动保存的接口
          // 假如改变的是文件,会触发很多次 uploading的时候也会触发
          // 所以需要判断 只有上传完成的照片才触发保存
          // 删除的时候 可能是一个空数组，也要触发保存
          const fileNameList = [
            'carPurchaseInvoice',
            'trafficInsurance',
            'businessInsurance',
            'purchaseTaxReceipt',
            'drivingLicense',
            'carRegistration',
            'gpsSysScreenshot',
            'guaranteeLetter',
            'otherEnclosure',
            'carPurchaseContract',
          ];

          const fieldNameList = ['invoiceDate'];

          const names = Object.keys(changeValues);
          const name = names[0];
          if (fileNameList.includes(name)) {
            const files = changeValues[name];
            if (!files.length) {
              // 删除的时候 可能是一个空数组，也要触发保存
              applyData('save');
            }
            // 判断当前name下的fileList 的 filePath 是否都存在
            for (let j = 0; j < files?.length; j++) {
              const file = files[j];
              if (file.filePath) {
                // filePath
                if (j === files?.length - 1) {
                  // 都存在
                  console.log('file112233', file);
                  applyData('save');
                }
              } else {
                return;
              }
            }
          } else if (fieldNameList.includes(name)) {
            applyData('save');
          }
        }}
        submitter={{ render: () => null }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
          <ProFormTextArea
            name="otherMsg"
            width="md"
            fieldProps={{
              maxLength: 500,
              style: { width: '100%' },
              onBlur: () => {
                applyData('save');
              },
            }}
            label="备注"
            placeholder="请输入"
            labelCol={{ span: 2 }}
          />
          <Form.Item label={'支持扩展名'} labelCol={{ span: 2 }}>
            <div>.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M</div>
          </Form.Item>
          <div style={{ display: 'flex', gap: 20 }}>
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                label="购车发票"
                name="carPurchaseInvoice"
                isNeedOCR={true}
                applyData={applyData}
                required
                rules={[{ required: true, message: '购车发票是必填的' }]}
                status={status}
                ocrData={carPurchaseInvoiceOcr}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>

            <div style={{ flex: 1 }}>
              {!JSON.parse(carPurchaseInvoiceOcr)?.ocrValueList?.length && '暂无OCR识别数据'}
              <Descriptions column={1} bordered size="small">
                {JSON.parse(carPurchaseInvoiceOcr)?.ocrValueList?.map((ocrValue: IocrItem) => {
                  const { fieldValue, fieldName, result } = ocrValue;
                  if (fieldName === '发动机号') return <></>;
                  return (
                    <Descriptions.Item label={fieldName} key={fieldName}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>{fieldValue || '-'}</span>
                        <span style={{ color: result ? 'green' : 'red' }}>
                          {result ? '校验成功' : '校验失败'}
                        </span>
                      </div>
                    </Descriptions.Item>
                  );
                })}
              </Descriptions>
            </div>
          </div>
          <div style={{ display: 'flex' }}>
            <div style={{ flex: 0.5 }}>
              <ProFormDatePicker
                name="invoiceDate"
                label="开票日期"
                labelCol={{ span: 4 }}
                width="sm"
                rules={[{ required: true, message: '请选择开票日期' }]}
              />
            </div>
          </div>
          <div style={{ display: 'flex', gap: 20 }}>
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                label="交强险保单"
                ocrData={trafficInsuranceOcr}
                required
                rules={[{ required: true, message: '交强险保单是必填的' }]}
                name="trafficInsurance"
                isNeedOCR={true}
                applyData={applyData}
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>
            <div style={{ flex: 1 }}>
              {!JSON.parse(trafficInsuranceOcr)?.ocrValueList?.length && '暂无OCR识别数据'}
              <Descriptions column={1} bordered size="small">
                {JSON.parse(trafficInsuranceOcr)?.ocrValueList?.map((ocrValue: IocrItem) => {
                  const { fieldValue, fieldName, result } = ocrValue;
                  return (
                    <Descriptions.Item label={fieldName} key={fieldName}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>{fieldValue || '-'}</span>
                        <span style={{ color: result ? 'green' : 'red' }}>
                          {result ? '校验成功' : '校验失败'}
                        </span>
                      </div>
                    </Descriptions.Item>
                  );
                })}
              </Descriptions>
            </div>
          </div>

          <div style={{ display: 'flex', gap: 20 }}>
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                ocrData={businessInsuranceOcr}
                required
                rules={[{ required: true, message: '商业险保单是必填的' }]}
                label="商业险保单"
                name="businessInsurance"
                isNeedOCR={true}
                applyData={applyData}
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>
            <div style={{ flex: 1 }}>
              {!JSON.parse(businessInsuranceOcr)?.ocrValueList?.length && '暂无OCR识别数据'}
              <Descriptions column={1} bordered size="small">
                {JSON.parse(businessInsuranceOcr)?.ocrValueList?.map((ocrValue: IocrItem) => {
                  const { fieldValue, fieldName, result } = ocrValue;
                  return (
                    <Descriptions.Item label={fieldName} key={fieldName}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>{fieldValue || '-'}</span>
                        <span style={{ color: result ? 'green' : 'red' }}>
                          {result ? '校验成功' : '校验失败'}
                        </span>
                      </div>
                    </Descriptions.Item>
                  );
                })}
              </Descriptions>
            </div>
          </div>
          <div style={{ display: 'grid', 'grid-template-columns': 'repeat(2, 1fr)', gap: 20 }}>
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                rules={[{ required: false }]}
                label="购置税票"
                name="purchaseTaxReceipt"
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                rules={[{ required: false }]}
                label="行驶证"
                name="drivingLicense"
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                rules={[{ required: false }]}
                label="车辆登记证"
                name="carRegistration"
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
                size={20}
              />
            </div>
            <div style={{ flex: 1, display: 'flex' }}>
              <div style={{ flex: 1 }}>
                <LoanProFormUploadDragger
                  label="GPS系统截图"
                  name="gpsSysScreenshot"
                  required={status === LOAN_MANAGE_CODE.WAIT_FIRST_INSTANCE}
                  rules={[
                    {
                      // 待一审状态，渠道不是中瑞供应商，gps截图必填
                      required: status === LOAN_MANAGE_CODE.WAIT_FIRST_INSTANCE,
                      message: 'GPS系统截图是必填的',
                    },
                  ]}
                  status={status}
                  noGuaranteeTypes={noGuaranteeTypes}
                  customHeader={
                    gpsStatusList?.length > 0 && (
                      <div style={{ flex: 1 }}>
                        {gpsStatusList?.map((item, index) => {
                          const { equipment, sbcStatus } = item;
                          return (
                            <Row gutter={[16, 16]} key={index}>
                              <Col>
                                <span>设备型号:</span>
                                <span>
                                  <span>{equipment || '-'}</span>
                                </span>
                              </Col>
                              <Col>
                                <span>运行状态:</span>
                                <span>
                                  <span
                                    style={{
                                      color: [GPS_STATUS.NOT_USE, GPS_STATUS.OFF_LINE].includes(
                                        sbcStatus,
                                      )
                                        ? 'red'
                                        : 'green',
                                    }}
                                  >
                                    {gpsStatusMap[sbcStatus]}
                                  </span>
                                </span>
                              </Col>
                            </Row>
                          );
                        })}
                      </div>
                    )
                  }
                />
              </div>
            </div>

            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                label="担保函"
                name="guaranteeLetter"
                required={!noGuaranteeTypes}
                rules={[{ required: noGuaranteeTypes ? false : true, message: '担保函是必填的' }]}
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>
            {orderDetail?.licenseType === LICENSE_TYPE.PERSONAL && (
              <div style={{ flex: 1 }}>
                <LoanProFormUploadDragger
                  label="赋强公证"
                  name="notarization"
                  required={false}
                  status={status}
                  noGuaranteeTypes={noGuaranteeTypes}
                  customHeader={
                    <div
                      style={{
                        color:
                          data?.notarizationStatus === NOTARIZATION_STATUS.CARD_DONE
                            ? 'green'
                            : 'red',
                      }}
                    >
                      {NOTARIZATION_STATUS_MAP.get(data?.notarizationStatus)}
                    </div>
                  }
                />
              </div>
            )}

            {orderDetail?.licenseType === LICENSE_TYPE.COMPANY && (
              <div style={{ flex: 1 }}>
                <LoanProFormUploadDragger
                  label="挂靠协议"
                  name="franchiseAgreement"
                  required
                  status={status}
                  noGuaranteeTypes={noGuaranteeTypes}
                  rules={[{ required: true, message: '挂靠协议是必填的' }]}
                  size={20}
                />
              </div>
            )}
            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                label="购车合同"
                name="carPurchaseContract"
                size={20}
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>

            <div style={{ flex: 1 }}>
              <LoanProFormUploadDragger
                label="其他附件"
                required
                rules={[{ required: true, message: '其他附件是必填的' }]}
                name="otherEnclosure"
                extra="支持扩展名：.pdf.png.jpg.jpeg.gif.bmp,.zip,.rar,.7z单个文件最大100M"
                accept=".pdf,.png,.jpg,.jpeg,.gif,.bmp,.zip,.rar,.7z"
                max={30}
                size={100}
                sceneCode={1}
                status={status}
                noGuaranteeTypes={noGuaranteeTypes}
              />
            </div>
          </div>
        </div>
      </ProForm>
    </>
  );
};

export default LoanResource;
