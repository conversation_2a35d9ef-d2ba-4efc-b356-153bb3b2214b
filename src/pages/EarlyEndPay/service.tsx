import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { AuditParams, EarlyEndListParams, ExportParams } from './data';

// 获取提前结清列表
export function getEarlyEndList(params: EarlyEndListParams) {
  return request('/bizadmin/repayment/offlineRemit/prepay/apply/list', {
    method: 'POST',
    data: params,
    ifTrimParams: true,
    headers,
  });
}

// 获取提前结清详情
export function getEarlyEndDetail(id: string) {
  return request(`/repayment/cms/prepay/apply/detail/${id}`, {
    method: 'GET',
  });
}

// 初审
export function auditEarlyEnd(data: AuditParams) {
  return request('/repayment/cms/prepay/apply/audit', {
    headers: {
      productCode: '0401', //融租产品code
    },
    method: 'POST',
    data,
  });
}

// 导出
export function exportEarlyEndList(data: ExportParams) {
  return request('/bizadmin/repayment/offlineRemit/prepay/apply/exportAsync', {
    method: 'POST',
    data,
    ifTrimParams: true,
    headers,
  });
}
