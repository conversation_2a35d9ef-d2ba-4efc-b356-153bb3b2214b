import HeaderTab from '@/components/HeaderTab/index';
import { disableFutureDate } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, Link } from '@umijs/max';
import { Radio } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import StatusModal from './components/StatusModal';
import type { EarlyEndPayItem } from './data';
import { exportEarlyEndList, getEarlyEndList } from './service';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps, removeBlankFromObject } from '@/utils/tools';

const statusMap = {
  1: '待初审',
  2: '初审驳回',
  3: '待复审',
  4: '复审驳回',
  5: '复审通过',
  6: '撤销',
};

const CallPay: React.FC<any> = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [activeTabKey, setActiveTabKey] = useState('3');
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState();

  function getSearchParams() {
    const { createdAt, ...data } = formRef?.current?.getFieldsValue();
    let newForm = { ...data };
    if (createdAt?.length) {
      const createStartTime = `${createdAt[0].format('YYYY-MM-DD')}`;
      const createEndTime = `${createdAt[1].format('YYYY-MM-DD')}`;
      newForm = { ...data, createStartTime, createEndTime };
    }
    return removeBlankFromObject(filterProps(newForm));
  }

  function handleTabChange(e) {
    const key = e.target.value;
    setActiveTabKey(key);
    if (key === '1') history.push('/businessMng/postLoanMng/callpay?tab=1');
    if (key === '2') history.push('/businessMng/postLoanMng/callpay?tab=2');
    if (key === '3') history.push('/businessMng/postLoanMng/early-end-pay');
  }

  const columns: ProColumns<CallPayItem>[] = [
    {
      title: '回款单号',
      dataIndex: 'prepayNo',
    },
    {
      title: '关联单号',
      dataIndex: 'orderNo',
      render: (_, row) => {
        let dom = <></>;
        //循环额度的不跳转
        if (row?.orderNo?.includes(',')) {
          dom = <>{row?.orderNo}</>;
        } else {
          const isCarInsurance = row?.productCode?.substring(0, 4);
          const carInsuranceUrl = `/businessMng/car-insurance/detail?orderNo=${row.orderNo}`;
          <Link
            to={
              isCarInsurance
                ? carInsuranceUrl
                : `/businessMng/postLoanMng/after-loan-detail?orderNo=${row.orderNo}&productCode=0201`
            }
          >
            {row.orderNo}
          </Link>;
        }
        return dom;
      },
    },
    {
      title: '申请人',
      dataIndex: 'userName',
    },
    {
      title: '订单金额（元）',
      dataIndex: 'orderAmount',
      search: false,
    },
    {
      title: '借款人姓名',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '回款日期',
      dataIndex: 'prepayTime',
      search: false,
    },
    {
      title: '回款金额',
      dataIndex: 'prepayAmount',
      key: 'prepayAmount',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.createdAt;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          createStartTime: `${value[0].split(' ')[0]}`,
          createEndTime: `${value[1].split(' ')[0]}`,
        }),
      },
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: {
        1: { text: '待初审' },
        2: {
          text: '初审驳回',
        },
        3: {
          text: '待复审',
        },
        4: {
          text: '复审驳回',
        },
        5: {
          text: '复审通过',
        },
        6: {
          text: '撤销',
        },
      },
      render: (_, row) => (
        <>
          <a
            onClick={() => {
              setStatusModalVisible(true);
              setCurrentRow(row.statusInfoDTOList);
            }}
          >
            {statusMap[row.status]}
          </a>
        </>
      ),
    },
    {
      title: '操作',
      width: 180,
      fixed: 'right',
      valueType: 'option',
      render: (_, row) => (
        <>
          <Link to={`/businessMng/postLoanMng/early-end-detail?id=${row.id}`}>查看详情</Link>
        </>
      ),
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<EarlyEndPayItem>
          columns={columns}
          actionRef={actionRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          rowKey="paymentId"
          search={{
            labelWidth: 100,
          }}
          headerTitle={
            <div>
              <Radio.Group
                // defaultValue="1"
                onChange={handleTabChange}
                buttonStyle="solid"
                optionType="button"
                value={activeTabKey}
              >
                <Radio.Button value="1">催收回款</Radio.Button>
                <Radio.Button value="2">还款审核</Radio.Button>
                <Radio.Button value="3">提前结清</Radio.Button>
              </Radio.Group>
            </div>
          }
          toolBarRender={() => {
            return [
              <AsyncExport
                key="export"
                getSearchDataTotal={async () => {
                  const data = await getEarlyEndList(getSearchParams());
                  return data?.total;
                }}
                getSearchParams={getSearchParams}
                exportAsync={exportEarlyEndList}
                taskCode={[ItaskCodeEnValueEnum.CALL_PAYMENT_EARLY_SETTLEMENT]}
              />,
            ];
          }}
          request={(params) => getEarlyEndList(params)}
        />
        <StatusModal
          onOk={async () => {
            setStatusModalVisible(false);
            setCurrentRow(undefined);
            // if (actionRef.current) {
            //   actionRef.current.reload();
            // }
          }}
          onCancel={() => {
            setStatusModalVisible(false);
            setCurrentRow(undefined);
          }}
          statusModalVisible={statusModalVisible}
          data={currentRow || []}
        />
      </PageContainer>
    </>
  );
};

export default CallPay;
