/*
 * @Author: your name
 * @Date: 2021-03-31 15:45:39
 * @LastEditTime: 2024-08-21 16:23:43
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/cash-order-detail.tsx
 */
import { DividerTit } from '@/components';
import HeaderTab from '@/components/HeaderTab/index';
import ShowInfo from '@/components/ShowInfo/index';
import StepProgress from '@/components/StepProgress/index';
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { getLoanInfo, getRepayPlan } from '@/services/global';
import {
  desensitizationBankAndIdCard,
  desensitizationPhone,
  isExternalNetwork,
} from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Card, Modal, Popover, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { reportLog } from '../Collection/services';
import { exPrivateStatusMap, merchantStatusMap, privateStatusMap } from '../PersonalIncoming/const';
import { getIntoDetail } from '../PersonalIncoming/service';
import { Contract, IncomingInfo, LoanCashInfo, RepayInfo } from './components';
import CardRecordTable from './components/CardRecordTable';
import { CUSTOMER_TYPE_MAP } from './const';
import { orderStatus, queryOrderDetail, queryOrderExtend } from './service';
import type { I_bankInfo, I_baseRiskData } from './types';

const CashOrderDetail: React.FC<any> = () => {
  const [cardRecordModalVisible, setCardRecordModalVisible] = useState<boolean>(false);

  const { orderNo } = history.location.query as any;
  const [baseRiskData, setBaseRiskData] = useState<Record<string, any>>({});
  const { data } = useRequest(() => {
    return queryOrderDetail(orderNo);
  });

  const { data: dataExtend } = useRequest(() => {
    return queryOrderExtend(orderNo);
  });
  const { data: repayPlanData } = useRequest(() => {
    return getRepayPlan(orderNo);
  });
  const { data: loanData } = useRequest(() => {
    return getLoanInfo(orderNo);
  });

  // const bindCarInfo = {
  //   engineCode:dataExtend?.carInfo?.engineCode,
  //   licenseCode:dataExtend?.carInfo?.licenseCode,
  //   carUniqueCode:dataExtend?.carInfo?.carUniqueCode
  // }
  // todo 获取银行卡信息
  // 银行卡信息配置
  const cardInfoMap = {
    bankName: '银行',
    bankCode: '银行卡号',
    phone: '银行预留手机号',
  };

  const { data: statusLogs } = useRequest(() => {
    return orderStatus(orderNo);
  });

  // const { data: baseData, loading: baseLoading } = useRequest(() => {
  //   return getIntoDetail(incomeNo);
  // });

  useEffect(() => {
    // 获取进件详情
    if (data?.orderReceiptNo) {
      getIntoDetail(data?.orderReceiptNo).then((res) => {
        setBaseRiskData(res.data);
      });
    }
  }, [data]);
  // //风控接口获取
  // const { data: baseInfo } = useRequest(() => {
  //   return incomeDetail(incomeNo);
  // });

  const basicMap = {
    orderNo: '进件流水号',
    userId: '用户ID',
    registerPhone: '注册手机号',
    registerTime: '注册时间',
    // riskStartTime: '风控进件时间',
    status: '进件状态',
    customerType: '用户类型',
    externalUserId: '三方ID',
  };

  const incomeMap = {
    status: '风控状态',
    rejectReason: '拒绝原因',
    riskStartTime: '风控进件时间',
    creditEndTime: '风控审核时间',
    creditAmount: '授信额度',
    creditInterestRate: '授信利率',
    remark: '备注',
  };
  const incomeMapNoReason = {
    status: '风控状态',
    riskStartTime: '风控进件时间',
    creditEndTime: '风控审核时间',
    creditAmount: '授信额度',
    creditInterestRate: '授信利率',
    remark: '备注',
  };
  const incomeMapTerminal =
    baseRiskData?.status === 40 || baseRiskData?.status === 35 ? incomeMap : incomeMapNoReason; //历史小易速贷，和融合之后的拒绝
  const getStatusMap = () => {
    if (
      baseRiskData?.history030101Income ||
      baseRiskData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
    ) {
      //历史小易速贷 或者是融租
      return exPrivateStatusMap;
    } else if (baseRiskData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.MERCHANT) {
      return merchantStatusMap;
    } else {
      return privateStatusMap;
    }
  };
  const riskMap = {
    // 如果是旧进件状态
    status: getStatusMap(), //非订单状态和进件详情状态保持一致
    repayType: {
      1: '一次本息',
      2: '等额本息',
    },
    customerType: CUSTOMER_TYPE_MAP,
  };

  const selfDefine = {
    orderNo: (
      <a
        onClick={() => {
          history.push(`/userMng/personalMng/detail?orderNo=${data?.orderReceiptNo}`);
        }}
      >
        {/* 进件单号 */}
        {data?.orderReceiptNo}
      </a>
    ),
  };

  function getInfoColumns() {
    const columns: ProColumns<I_baseRiskData>[] = [
      {
        title: '进件流水号',
        dataIndex: 'orderNo',
        render(_, record) {
          return isExternalNetwork() ? (
            data?.orderReceiptNo
          ) : (
            <a
              onClick={() => {
                history.push(`/userMng/personalMng/detail?orderNo=${record?.orderReceiptNo}`);
              }}
            >
              {/* 进件单号 */}
              {data?.orderReceiptNo}
            </a>
          );
        },
      },
      {
        title: '用户ID',
        dataIndex: 'userId',
      },
      {
        title: '注册手机号',
        dataIndex: 'registerPhone',
        render(_, record) {
          if (!record.registerPhone) {
            return '-';
          }
          return isExternalNetwork() ? (
            <>
              {desensitizationPhone(record?.registerPhone)}
              <Popover content={_} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (record?.registerPhone) {
                      await reportLog(record?.registerPhone);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          ) : (
            <>{record.registerPhone}</>
          );
        },
      },
      {
        title: '注册时间',
        dataIndex: 'registerTime',
      },
      {
        title: '进件状态',
        dataIndex: 'status',
        valueEnum: getStatusMap(),
        valueType: 'select',
      },
      {
        title: '用户类型',
        dataIndex: 'customerType',
        valueType: 'select',
        valueEnum: CUSTOMER_TYPE_MAP,
      },
      { dataIndex: 'externalUserId', title: '三方ID' },
    ];
    return columns;
  }

  function getBankInfoColumns() {
    const columns: ProColumns<I_bankInfo>[] = [
      {
        dataIndex: 'bankName',
        title: '银行',
      },
      {
        dataIndex: 'bankCode',
        title: '银行卡号',
        render(_, record) {
          return record?.bankCode ? desensitizationBankAndIdCard(record?.bankCode) : '-';
        },
      },
      {
        dataIndex: 'phone',
        title: '银行预留手机号',
        render(_, record) {
          return (
            <>
              {record.phone ? (
                <>
                  {desensitizationPhone(record?.phone)}
                  <Popover content={_} trigger="click">
                    <a
                      onClick={async () => {
                        // 发送查看日志
                        if (record?.phone) {
                          await reportLog(record?.phone);
                        }
                      }}
                    >
                      查看
                    </a>
                  </Popover>
                </>
              ) : (
                '-'
              )}
            </>
          );
        },
      },
    ];
    return columns;
  }

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <StepProgress
          stepStatus={statusLogs?.map((item: { auditTime: string; statusDesc: string }) => {
            return {
              bol: false,
              desc: item.statusDesc,
              localDate: item.auditTime,
            };
          })}
        />
        <Card title="基础信息" style={{ marginTop: 20 }}>
          <DividerTit title="基本信息" style={{ marginTop: 10 }} />
          {isExternalNetwork() ? (
            <ProDescriptions
              dataSource={baseRiskData as any}
              columns={getInfoColumns()}
              style={{ marginTop: 10, marginLeft: 10 }}
            />
          ) : (
            <ShowInfo
              noCard
              selfDefine={selfDefine}
              infoMap={basicMap}
              data={baseRiskData}
              // loading={baseLoading}
              itemMap={riskMap}
            />
          )}
        </Card>
        <Card title="风控信息" style={{ marginTop: 20 }}>
          <ShowInfo noCard infoMap={incomeMapTerminal} data={baseRiskData} itemMap={riskMap} />
        </Card>
        <Card title="借款信息" style={{ marginTop: 20 }}>
          <Tabs>
            <Tabs.TabPane key="1" tab="基础信息">
              <IncomingInfo data={data} tableData={repayPlanData?.listRspList || []} />
            </Tabs.TabPane>
            <Tabs.TabPane key="2" tab="银行卡">
              {/* 银行卡 */}
              <a
                href="javascript:;"
                style={{ display: 'block', textAlign: 'right' }}
                onClick={() => setCardRecordModalVisible(true)}
              >
                银行卡日志
              </a>
              {isExternalNetwork() ? (
                <ProDescriptions
                  dataSource={dataExtend?.bankcardInfo as any}
                  columns={getBankInfoColumns()}
                />
              ) : (
                <ShowInfo noCard infoMap={cardInfoMap} data={dataExtend?.bankcardInfo} />
              )}
            </Tabs.TabPane>
            <Tabs.TabPane key="3" tab="合同">
              <Contract
                dataContract={dataExtend?.contractInfo}
                orderNo={orderNo}
                isXiaoYiSifang={loanData?.lendingMaster === '乐享借'}
                userName={data?.userName}
              />
            </Tabs.TabPane>
          </Tabs>
        </Card>

        {/* 银行卡日志 */}
        <Modal
          title="银行卡日志"
          width="60%"
          forceRender
          open={cardRecordModalVisible}
          footer={null}
          onCancel={() => setCardRecordModalVisible(false)}
        >
          <CardRecordTable orderNo={orderNo} />
        </Modal>

        <LoanCashInfo loanData={loanData} />
        <RepayInfo
          orderNo={orderNo}
          productCode={baseRiskData?.productCode}
          accountNumber={baseRiskData?.userId}
          accountName={baseRiskData?.userName}
        />
      </PageContainer>
    </>
  );
};

export default CashOrderDetail;
