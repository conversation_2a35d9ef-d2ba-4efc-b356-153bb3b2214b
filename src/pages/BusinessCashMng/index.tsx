/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-08-26 13:54:00
 * @modify date 2021-08-26 13:54:00
 * @desc 业务管理 - 订单管理(小贷)
 */

import HeaderTab from '@/components/HeaderTab/index';
import LoadingButton from '@/components/LoadingButton';
import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { getProductNameEnum } from '@/services/enum';
import { getRandomUUID } from '@/utils/tools';
import { disableFutureDate } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, Link } from '@umijs/max';
import { BlobWriter, ZipWriter } from '@zip.js/zip.js';
import { message, Modal, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { KeepAlive, useAliveController } from 'react-activation';
import { CUSTOMER_TYPE_MAP, merchantMap, privateOrderStatusMap } from './const';
import type { IcontractItem, OrderListItem, OrderListParams } from './data';
import { exportContract, orderExport, queryOrder } from './service';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import type { ProFormInstance } from '@ant-design/pro-components';

const CashOrderList: React.FC<any> = () => {
  const optionsMap = (mapStatus: any) => {
    return Object.keys(mapStatus).map((item) => {
      return {
        value: item,
        label: mapStatus[item],
      };
    });
  };
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});

  useEffect(() => {
    getProductNameEnum(PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN).then((data) => {
      setProductCodeValueEnum(
        data.reduce((pre, cur) => {
          if (
            [
              SECONDARY_CLASSIFICATION_CODE.MERCHANT,
              SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
            ].includes(cur.value.substring(0, 4) as any)
          ) {
            // 只有圆易借 和 圆商贷 弄进来
            return {
              ...pre,
              [cur.value]: cur.label,
            };
          } else {
            return {
              ...pre,
            };
          }
        }, {}),
      );
    });
  }, []);

  const [statusOptions, setStatusOption] = useState<{ label: string; value: string }[]>(
    optionsMap(privateOrderStatusMap),
  );

  const getSearchParams = () => {
    const { applyTime, lendingTime, ...data } = formRef?.current?.getFieldsValue();
    let newForm: OrderListParams = { ...data };
    // 借款时间
    if (applyTime?.length) {
      const applyTimeStart = `${applyTime[0].format('YYYY-MM-DD')} 00:00:00`;
      const applyTimeEnd = `${applyTime[1].format('YYYY-MM-DD')} 23:59:59`;
      newForm = { ...data, applyTimeStart, applyTimeEnd };
    }
    // 放款时间
    if (lendingTime?.length) {
      const lendingTimeStart = `${lendingTime[0].format('YYYY-MM-DD')} 00:00:00`;
      const lendingTimeEnd = `${lendingTime[1].format('YYYY-MM-DD')} 23:59:59`;
      newForm = { ...data, lendingTimeStart, lendingTimeEnd };
    }
    return removeBlankFromObject(filterProps(newForm));
  };

  const { query } = history.location;

  useEffect(() => {
    // 重置无法清除 initialValue 所以这么设置一下
    formRef.current?.setFieldsValue({
      orderNo: query?.orderNo,
      userNo: query?.userNo,
    });
    // request 的时候 有几率拿不到 表单值 所以主动请求
    const timerId = setTimeout(() => {
      // actionRef.current?.reload();
      formRef.current?.submit();
    }, 600);

    return () => {
      clearTimeout(timerId);
    };
  }, []);
  const columns: ProColumns<OrderListItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '进件流水号',
      dataIndex: 'orderReceiptNo',
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      render: (_, row) => {
        return (
          <a
            onClick={() => {
              if (row.orderStatus < 40 || row.orderStatus === 41) {
                message.error('风控未审核完成，用户详情未生成');
                return;
              }
              history.push(
                `/userMng/personalMng/com-detail?userNo=${row?.userNo}&productSecondCode=${row?.secondTypeCode}`,
              );
            }}
          >
            {row?.userNo}
          </a>
        );
      },
      // search: false,
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '用户类型',
      dataIndex: 'customerType',
      valueEnum: CUSTOMER_TYPE_MAP,
    },
    {
      title: '资金渠道',
      dataIndex: 'channelCode',
      valueEnum: {
        BAI_XIN: '百信',
        YI_REN_XING: '易人行',
        LE_XIANG_JIE: '乐享借',
        WEI_XIN_JIN_KE: '维信金科',
        MA_SHANG_XIAO_FEI: '马上消费',
        XIAO_YING_KA_DAI: '小赢卡贷',
        TIAN_CHEN_JIN_RONG: '甜橙金融',
        FEI_QUAN: '飞泉',
        JING_DONG_YUN_GONG_CHANG: '京银融',
        HUO_SHAN_RONG: '火山融',
      },
      hideInTable: true,
    },
    {
      title: '资金渠道',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondTypeCode',
      key: 'secondTypeCode',
      initialValue: '0301',
      order: 1,
      valueEnum: {
        '0301': '圆易借',
        '0304': '圆商贷',
      },
      hideInTable: true,
      fieldProps: {
        onChange: () => {
          formRef.current?.setFieldValue('productCode', undefined);
        },
        onSelect: (val: string) => {
          let options: any = [];
          //  如果是圆商贷，状态不同
          if (val === SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY) {
            options = optionsMap(privateOrderStatusMap);
          } else {
            options = optionsMap(merchantMap);
          }
          setStatusOption(options);
        },
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueEnum: () => {
        const secondTypeCode = formRef.current?.getFieldValue('secondTypeCode');

        // 由于产品是多选，所以不做反显二级分类
        const codeMap = {};
        for (const value in productCodeValueEnum) {
          if (value.substring(0, 4) === secondTypeCode) {
            // 圆易借
            codeMap[value] = productCodeValueEnum[value];
          }
        }
        // 二级不存在 则展示全部
        return secondTypeCode ? codeMap : productCodeValueEnum;
      },
      valueType: 'select',
      // request: () => getProductNameEnum(PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN),
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },

    {
      title: '借款金额',
      dataIndex: 'applyAmount',
      search: false,
    },
    {
      title: '借款期限',
      dataIndex: 'loanTerm',
      search: false,
    },
    {
      title: '借款利率',
      dataIndex: 'loanInterestRate',
      search: false,
    },
    {
      title: '借款时间',
      dataIndex: 'applyTime',
      valueType: 'dateRange',
      render(dom, row) {
        return row?.applyTime;
      },
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          applyTimeStart: `${value[0].split(' ')[0]} 00:00:00`,
          applyTimeEnd: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
      valueType: 'dateRange',
      render(dom, row) {
        return row?.lendingTime || '-';
      },
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      search: {
        transform: (value: any) => ({
          lendingTimeStart: `${value[0].split(' ')[0]} 00:00:00`,
          lendingTimeEnd: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      valueType: 'select',
      fieldProps: {
        options: statusOptions,
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, record) => {
        const { orderStatus, secondTypeCode } = record;
        if (secondTypeCode === SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY) {
          return privateOrderStatusMap[orderStatus] || '-';
        }
        return merchantMap[orderStatus] || '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <>
          <Link to={`/businessMng/cash-detail?orderNo=${record.orderNo}`}>查看详情</Link>
        </>
      ),
    },
  ];

  const showErrorConTractListJsx = (errorContractList: IcontractItem[]) => {
    if (errorContractList?.length) {
      // 展示出错误的地址出来 有些oss地址可能有问题
      Modal.warning({
        width: 1000,
        content: (
          <div>
            <div>如下数据下载失败,请联系开发人员核实!!!</div>
            <ProTable
              // ossPath,contractName,contractNo,orderNo‘
              size={'small'}
              search={false}
              toolbar={{
                settings: [],
              }}
              pagination={{
                defaultPageSize: 10,
              }}
              dataSource={errorContractList}
              columns={[
                {
                  dataIndex: 'ossPath',
                  width: 300,
                  title: '合同地址',
                  ellipsis: true,
                  copyable: true,
                },
                { dataIndex: 'contractName', title: '合同名称' },
                { dataIndex: 'contractNo', title: '合同编号' },
                { dataIndex: 'orderNo', title: '订单号' },
              ]}
            />
          </div>
        ),
      });
    }
  };

  const getExportContractParams = () => {
    const {
      applyTime,
      lendingTime,
      secondTypeCode: productSecondCode,
      productCode: productCodeList,
      orderStatus,
      ...data
    } = formRef?.current?.getFieldsValue();
    const params = {
      ...data,
      productSecondCode,
      productCodeList: productCodeList?.join(','),
      orderStatus: orderStatus?.join(','),
      channelCode: 'YI_REN_XING',
    };
    // 借款时间
    if (applyTime?.length) {
      const createStartTime = `${applyTime[0].format('YYYY-MM-DD')} 00:00:00`;
      const createEndTime = `${applyTime[1].format('YYYY-MM-DD')} 23:59:59`;
      params.createStartTime = createStartTime;
      params.createEndTime = createEndTime;
    }

    // 放款时间
    if (lendingTime?.length) {
      const lendingStartTime = `${lendingTime[0].format('YYYY-MM-DD')} 00:00:00`;
      const lendingEndTime = `${lendingTime[1].format('YYYY-MM-DD')} 23:59:59`;
      params.lendingStartTime = lendingStartTime;
      params.lendingEndTime = lendingEndTime;
    }

    return params;
  };

  async function getZipFileBlob(contractList: IcontractItem[], errorContractList: IcontractItem[]) {
    const zipWriter = new ZipWriter(new BlobWriter('application/zip'));
    await Promise.all(
      contractList.map(async (item) => {
        const { contractName, ossPath, orderNo, contractNo } = item;
        const response = await fetch(ossPath);
        const { body, status } = response || {};
        if (status === 200) {
          // 只有200 才会 被下载成文件 // getUuid 防止文件重复 导致报错
          return zipWriter.add(
            `${orderNo}-${contractName}-${getRandomUUID(8)}.pdf`,
            // 用zip库的HttpReader会有问题
            // new HttpReader(ossPath, {
            //   useXHR: true,
            // }),
            // new HttpReader 返回的是可读流
            body || undefined,
          );
        } else {
          // 其他状态码 要提示出错误来
          errorContractList.push({
            ossPath,
            contractName,
            contractNo,
            orderNo,
          });
          return;
        }
      }),
    );
    return zipWriter.close();
  }

  function downloadFile(blob: Blob | MediaSource) {
    const url = URL.createObjectURL(blob);
    const a = Object.assign(document.createElement('a'), {
      download: '小贷合同.zip',
      href: url,
    });
    a.click();
    // 经过测试 -- 如果不移除，则会一直占用内存
    URL.revokeObjectURL(url);
  }

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<OrderListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="orderNo"
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return queryOrder(params);
          }}
          search={{
            labelWidth: 90,
            defaultCollapsed: false,
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              <AsyncExport
                key="export"
                getSearchDataTotal={async () => {
                  const data = await queryOrder(getSearchParams());
                  return data?.total;
                }}
                getSearchParams={getSearchParams}
                exportAsync={orderExport}
                taskCode={[ItaskCodeEnValueEnum.CASH_ORDER_MANAGER]}
              />,
              <LoadingButton
                key="exportBtn"
                onClick={async () => {
                  try {
                    const params = getExportContractParams();
                    // 这一步可能会导致超时 被catch捕获
                    const contract = await exportContract(params);
                    const contractList = Object.values(contract).flat(2);
                    if (!contractList.length) return;
                    // 非200的合同信息
                    const errorContractList: IcontractItem[] = [];
                    // 获取压缩过后的zip 二进制 todo: 函数不太纯
                    const blob = await getZipFileBlob(contractList, errorContractList);
                    // 某些合同地址可能有问题 比如 404 403 脏数据 等问题，将这些有问题的合同展示出来
                    showErrorConTractListJsx(errorContractList);
                    downloadFile(blob);
                  } catch (error) {
                    console.log('error', error);
                    Modal.error({
                      content: (
                        <div>
                          <h3>下载有误，请联系开发人员核实</h3>
                          <div>
                            <Tag color="error">{error?.message}</Tag>
                          </div>
                        </div>
                      ),
                    });
                  }
                }}
              >
                导出合同
              </LoadingButton>,
            ];
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => {
  const { refresh } = useAliveController();
  // 从 另外一个页面跳 到该列表 由于订单号是变的，要根据query条件赋值到列表查询中，查询到最新数据,  是不希望缓存的
  useEffect(() => {
    const isCache = history?.location?.query?.isCache !== '0';
    if (!isCache) {
      refresh('businessMng/cash-list');
    }
  }, []);
  return (
    <>
      <HeaderTab />
      <KeepAlive name={'businessMng/cash-list'}>
        <CashOrderList />
      </KeepAlive>
    </>
  );
};
