import type { IbillListItem, IbillListParams } from '@/pages/AfterLoan/carInsurance/types';

export interface I_baseRiskData {
  orderReceiptNo: any;
  orderNo: string;
  userId: string;
  registerPhone: string;
  registerTime: string;
  status: string;
  customerType: string;
  externalUserId?: string | null;
}

export interface I_bankInfo {
  bankName: string;
  bankCode: string;
  phone: string;
}

export enum IdimensionEnCode {
  TERM_BILL = 1, // 车辆纬度
  ORDER_TERM_BILL = 2, // 订单纬度
  SUBJECT_MATTER_BILL = 3, //总账
}

export type Idimension = 1 | 2 | 3; // TERM_BILL(1, "期账"), ORDER_TERM_BILL(2, "订单期账")    SUBJECT_MATTER_BILL(3, "标的物总账");

export interface NewIbillListItem extends IbillListItem {
  channelName: string;
  orderStatus: number;
  userNo: string;
}

export interface NewIbillListParams extends Omit<IbillListParams, 'secondaryClassification'> {
  current: number;
  pageSize: number;
  secondaryClassification: '0301';
}

// 账单状态枚举(融租没有70，80)
export const statusMap = {
  20: '待还款',
  30: '提前结清',
  40: '正常结清',
  50: '逾期',
  60: '逾期结清',
};
