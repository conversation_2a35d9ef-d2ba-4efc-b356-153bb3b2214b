/* eslint-disable react/no-array-index-key */
/*
 * @Author: your name
 * @Date: 2021-04-19 15:25:32
 * @LastEditTime: 2025-04-11 15:09:50
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/IncomingInfo.tsx
 */
import { ShowInfo } from '@/components';
import {
  GUARANTEE_TYPES,
  LEASE_LOAN_FILE_TYPE,
  LICENSE_TYPES_MAP,
  SECONDARY_CLASSIFICATION_CODE,
} from '@/enums';
import globalStyle from '@/global.less';
import { reportLog } from '@/pages/Collection/services';
import {
  exPrivateStatusMap,
  merchantStatusMap,
  privateStatusMap,
} from '@/pages/PersonalIncoming/const';
import { getFuncDom, sortArr } from '@/pages/PersonalIncoming/detail';
import {
  desensitizationBankAndIdCard,
  desensitizationPhone,
  isChannelStoreUser,
  isExternalDesensitization,
} from '@/utils/utils';
import { RedoOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import { history, useAccess } from '@umijs/max';
import { Button, Card, Col, List, message, Popover, Row, Table, Tabs } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useEffect, useState } from 'react';
import { ORDER_STATUS, SH_BANK_STATUS, statusLeaseMap } from '../consts';
import type { OrderListItem, RepayItem } from '../data';
import { checkIfShowRepushBtn, repushChannelApprove } from '../service';
import './index.less';
interface IncomingInfoProps {
  // orderNo: string
  data: OrderListItem;
  tableData: RepayItem[];
  pagination?: boolean;
  rowKey?: string;
  baseData?: any;
  inComeInfo?: any;
}

enum TABS_ITEM {
  USER_INFO = '1', // 用户信息
  BASE_INFO = '2', // 基础信息
  WORK_INFO = '3', // 工作信息
  RELATION_INFO = '4', // 联系信息
  FINANCE_INFO = '5', // 风控信息
  RISK_INFO = '6', // 金融方案
  IMAGE_INFO = '7', // 影像资料
}

const IncomingInfo: React.FC<IncomingInfoProps> = (props) => {
  const { data, tableData, baseData, inComeInfo } = props; // @ts-ignore
  const access = useAccess();
  const [activeTab, setActiveTab] = useState<string>(TABS_ITEM.USER_INFO);
  const [btnLoading, setBtnLoading] = useState(false);
  const [showRepushBtn, setShowRepushBtn] = useState(false);
  const [reepushDisabled, setRepushDisabled] = useState(false);

  const checkRepushBtn = async (orderNo: string) => {
    const res = await checkIfShowRepushBtn(orderNo);
    const { checkResult, reCreditUpperLimit } = res?.data || {};
    if (checkResult) {
      setShowRepushBtn(true);
    } else {
      setShowRepushBtn(false);
    }
    //已经达到推送上限，置灰按钮
    if (reCreditUpperLimit) {
      setRepushDisabled(true);
    }
  };

  useEffect(() => {
    if (data?.orderNo && data?.orderStatus === ORDER_STATUS.CHANNEL_APPROVE_REJECT) {
      checkRepushBtn(data?.orderNo);
    }
  }, [data?.orderNo, data?.orderStatus]);

  // 金融方案的数据
  const financeData: any = {
    ...data,
    usageOfLoan: baseData?.usageOfLoan,
  };

  let finallyTableData: RepayItem[] = [];
  // 处理为树形结构
  if (tableData.length > 1) {
    const children = tableData?.slice(1, tableData.length);
    finallyTableData = [{ ...tableData?.[0], children }];
  } else {
    finallyTableData = tableData;
  }

  // todo获取进件信息
  const incomeColumn = [
    {
      title: '期数',
      dataIndex: 'termDetail',
    },
    {
      title: '应还总额',
      dataIndex: 'amountDue',
      key: 'amountDue',
    },
    {
      title: '应还本金',
      dataIndex: 'principal',
      key: 'principal',
    },
    {
      title: '应还利息',
      dataIndex: 'interest',
      key: 'interest',
    },
    // {
    //   title: '费用',
    //   dataIndex: 'cost',
    //   key: 'cost',
    // },
    {
      title: '应还日期',
      dataIndex: 'repayTime',
      key: 'repayTime',
    },
  ];

  // 基础信息配置
  const baseInfoMap = {
    orderNo: '订单号',
    orderStatus: '订单状态',
    channel: '渠道商户',
    createdAt: '进件时间',
    orderReceiptNo: '进件流水号',
  };

  const baseInfoItemMap = {
    orderStatus: statusLeaseMap,
  };

  // 业务系统_融租渠道用户不做跳转
  const linkMap = {
    userNo: isChannelStoreUser(access)
      ? ''
      : `/userMng/personalMng/detail?orderNo=${data?.orderReceiptNo}`,
  };
  // 用户信息配置
  const userInfoMap = {
    userNo: '用户ID',
    userName: '用户名称',
    idNo: '证件号码',
    phone: '手机号',
    address: '居住地址',
    registerPhone: '注册手机号',
    externalUserId: '三方ID',
  };

  const selfDefineUserInfoMap = {
    userNo: (
      <a
        onClick={() => {
          // if (data.orderStatus < 40) {
          //   message.error('风控未审核完成，用户详情未生成');
          //   return;
          // }
          // if (data.orderStatus === 41) {
          //   message.error('风控未审核通过，用户详情未生成');
          //   return;
          // }
          history.push(`${linkMap.userNo}`);
        }}
      >
        {data?.userNo}
      </a>
    ),
  };

  const userInfoMapFilter = {
    '0100': 'userName',
    '0107': 'idNo',
    // '0111': 'address'
  };

  // 金融方案信息配置
  const financeInfoMap = {
    classificationDesc: '产品一级分类',
    secondaryClassificationDesc: '产品二级分类',
    productName: '产品名称',
    productCode: '产品ID',
    guaranteeType: '担保类型',
    guaranteePeriods: '担保期数',
    applyAmount: '申请额度',
    loanTerm: '还款期限',
    repayType: '还款方式',
    interestRate: '年利率',
    usageOfLoan: '借款用途',
    // transferPrice: '车辆转让价(元)',
    dealPrice: '车辆成交价(元)',
    firstPayAmount: '首付款(元)',
    creditAmount: '融资金额(元)',
    rentPrice: '月供(元)',
    funderChannelCode: '借款合同类型',
    licenseType: '上牌类型',
  };
  // console.log(baseData?.status, 'basedataStatus', data?.orderStatus, 'orderStatus');
  //订单状态在审核通过(40)之后状态展示融资金额，其他状态不展示
  if (!Number.isNaN(data?.orderStatus) && Number(data?.orderStatus) < 40) {
    delete financeInfoMap.creditAmount;
  }
  const selfDefineFinanceMap = {
    firstPayAmount:
      data?.firstPayRateStr !== '0%'
        ? `${data?.firstPayAmount}（${data?.firstPayRateStr}）`
        : data?.firstPayAmount,
    guaranteeType: GUARANTEE_TYPES[data?.guaranteeType] || '-',
    funderChannelCode: LEASE_LOAN_FILE_TYPE?.[data?.funderChannelCode as string] || '-',
    licenseType: LICENSE_TYPES_MAP[data?.licenseType as string] || '-',
  };

  const incomeMap = {
    status: '风控状态',
    rejectReason: '审核原因',
    riskStartTime: '风控进件时间',
    creditEndTime: '风控审核时间',
    preCreditAmount: '预授信额度',
    creditAmount: '授信额度',
    creditInterestRate: '授信利率',
    remark: '备注',
  };
  //预授信额度:预审拒绝16,审核通过40,降额待确认23、降额通过50不展示
  if (data?.orderStatus && [16, 40, 23, 50].includes(data?.orderStatus)) {
    delete incomeMap.preCreditAmount;
  }
  // 上海银行风控信息，运营可见，渠道不可见
  const shBankIncomeMap = {
    creditStatus: '上银授信结果状态',
    approvalFailReason: '审批失败原因',
    approvalFailDesc: '审批失败描述',
  };

  const selfDefineRiskMap = {
    interestRate: (
      <>{baseData?.interestRate ? `${new BigNumber(baseData?.interestRate).times(100)}%` : '-'}</>
    ),
    creditInterestRate: (
      <>
        {baseData?.creditInterestRate
          ? `${new BigNumber(baseData?.creditInterestRate).times(100)}%`
          : '-'}
      </>
    ),
    creditStatus: SH_BANK_STATUS[data?.creditStatus || ''],
    approvalFailDesc: (() => {
      if (!data?.approvalFailDesc) {
        return '-';
      }
      const dataSource = data?.approvalFailDesc?.split('|');
      return (
        <div className="scroll-body">
          <List
            dataSource={dataSource}
            renderItem={(item, index) => <List.Item>{item}</List.Item>}
          />
        </div>
      );
    })(),
  };
  const getStatusMap = () => {
    if (
      baseData?.history030101Income ||
      baseData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
    ) {
      //历史小易速贷 或者是融租
      return exPrivateStatusMap;
    } else if (baseData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.MERCHANT) {
      return merchantStatusMap;
    } else {
      return privateStatusMap;
    }
  };
  const riskMap = {
    status: getStatusMap(), //圆易借状态不同
    repayType: {
      1: '一次本息',
      2: '等额本息',
    },
    customerType: {
      1: '司机',
      2: '搬家小哥',
      3: '用户',
      4: '企业',
    },
  };

  function getInfoColumns() {
    const columns: ProColumns<OrderListItem>[] = [
      { dataIndex: 'userNo', title: '用户ID' },
      { dataIndex: 'userName', title: '用户名称' },
      {
        dataIndex: 'idNo',
        title: '证件号码',
        render(_, record) {
          return record.idNo ? desensitizationBankAndIdCard(record.idNo) : '-';
        },
      },
      {
        dataIndex: 'phone',
        title: '手机号',
        render(_, record) {
          return record.phone ? (
            <>
              {desensitizationPhone(record.phone)}
              <Popover content={_} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (record?.phone) {
                      await reportLog(record?.phone);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          ) : (
            '-'
          );
        },
      },
      {
        dataIndex: 'address',
        title: '居住地址',
        render(_, record) {
          return record.address ? '**********' : '-';
        },
      },
      { dataIndex: 'externalUserId', title: '三方ID' },
    ];
    return columns;
  }

  const handleRepush = async () => {
    try {
      if (!data?.orderNo) return;
      setBtnLoading(true);
      await repushChannelApprove(data?.orderNo);
      setBtnLoading(false);
      message.success('重新推送成功！');
    } catch {
      setBtnLoading(false);
    } finally {
      // 推送完检查按钮状态
      checkRepushBtn(data?.orderNo);
    }
  };

  return (
    <Card title="进件信息" className={globalStyle.mt30}>
      <Tabs
        tabBarExtraContent={{
          // 风控信息模块，上海银行资方授信审批拒绝要展示重新推送按钮
          right: activeTab === TABS_ITEM.RISK_INFO && !isChannelStoreUser(access) && showRepushBtn && (
            <Button
              type="primary"
              loading={btnLoading}
              disabled={reepushDisabled}
              onClick={() => {
                handleRepush();
              }}
            >
              <RedoOutlined />
              重新推送
            </Button>
          ),
        }}
        onChange={(key: string) => setActiveTab(key)}
      >
        <Tabs.TabPane tab="用户信息" key={TABS_ITEM.USER_INFO}>
          {isExternalDesensitization(access) ? (
            <ProDescriptions dataSource={data} columns={getInfoColumns()} />
          ) : (
            <ShowInfo noCard infoMap={userInfoMap} selfDefine={selfDefineUserInfoMap} data={data} />
          )}
          {/* 个人信息 */}
          {inComeInfo?.baseInfo?.length > 0 && (
            <Row className="personal-list" style={{ marginTop: 16 }}>
              {
                <>
                  {/* {sortArr(baseInfo?.[key], key)} 通过fieldCode筛选掉上面已经存在重复的字段 */}
                  {sortArr(inComeInfo?.baseInfo, 'baseInfo')
                    ?.filter((item: { fieldCode: string }) => {
                      return userInfoMapFilter[item?.fieldCode] === undefined;
                    })
                    ?.map(
                      (
                        item: {
                          fieldValue: string;
                          fieldDesc: string;
                          fieldCode: string;
                        },
                        index: number,
                      ) => {
                        return getFuncDom('baseInfo', item, index);
                      },
                    )}
                </>
              }
            </Row>
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab="基础信息" key={TABS_ITEM.BASE_INFO}>
          <ShowInfo noCard infoMap={baseInfoMap} data={data} itemMap={baseInfoItemMap} />
        </Tabs.TabPane>
        {inComeInfo?.workInfo && (
          <Tabs.TabPane tab="工作信息" key="item-0">
            {inComeInfo?.workInfo?.length > 0 ? (
              <Row className="personal-list" style={{ marginTop: 16 }}>
                {
                  <>
                    {sortArr(inComeInfo?.workInfo, 'workInfo')?.map(
                      (
                        item: {
                          fieldValue: string;
                          fieldDesc: string;
                          fieldCode: string;
                        },
                        index: number,
                      ) => {
                        return getFuncDom('workInfo', item, index);
                      },
                    )}
                  </>
                }
              </Row>
            ) : (
              '暂无数据'
            )}
          </Tabs.TabPane>
        )}
        {inComeInfo?.relativeInfo && (
          <Tabs.TabPane tab="联系信息" key={TABS_ITEM.RELATION_INFO}>
            {inComeInfo?.relativeInfo?.length > 0 ? (
              <Row className="personal-list" style={{ marginTop: 16 }}>
                {
                  <>
                    {sortArr(inComeInfo?.relativeInfo, 'relativeInfo')?.map(
                      (
                        item: {
                          fieldValue: string;
                          fieldDesc: string;
                          fieldCode: string;
                        },
                        index: number,
                      ) => {
                        return getFuncDom('relativeInfo', item, index);
                      },
                    )}
                  </>
                }
              </Row>
            ) : (
              '暂无数据'
            )}
          </Tabs.TabPane>
        )}
        <Tabs.TabPane tab="风控信息" key={TABS_ITEM.RISK_INFO}>
          <ShowInfo
            noCard
            infoMap={isChannelStoreUser(access) ? incomeMap : { ...incomeMap, ...shBankIncomeMap }}
            selfDefine={selfDefineRiskMap}
            data={{
              ...baseData,
              creditStatus: data?.creditStatus,
              approvalFailReason: data?.approvalFailReason,
              approvalFailDesc: data?.approvalFailDesc,
              rejectReason: data?.rejectReason,
              preCreditAmount: data?.preCreditAmount,
            }}
            itemMap={riskMap}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="金融方案" key={TABS_ITEM.FINANCE_INFO}>
          <Row className={globalStyle.pl20}>
            <ShowInfo
              noCard
              infoMap={financeInfoMap}
              selfDefine={selfDefineFinanceMap}
              data={financeData}
            />
            {/* {Object.keys(financeInfoMap).map((item, index) => {
              const value = data ? data[item] : '';
              return (
                // eslint-disable-next-line react/no-array-index-key
                !(item === 'firstPayAmount' && value === '0.00') && (
                  <Col span={8} key={item + index}>
                    <div className={globalStyle.lineHeight40}>
                      <span className={globalStyle.lineHeight40}>{financeInfoMap[item]}:</span>
                      <Text
                        copyable={value?.length * 14 > 270 ? { text: value } : false}
                        ellipsis={{ tooltip: value }}
                        className={globalStyle.ml20}
                        style={{ width: 270 }}
                      >
                        {item === 'firstPayAmount' && data && data.firstPayRateStr !== '0%'
                          ? `${value}（${data.firstPayRateStr}）`
                          : value}
                      </Text>
                    </div>
                  </Col>
                )
              );
            })} */}
          </Row>
          <Row className={globalStyle.pl20}>
            <Col span={2}>
              <span className={` ${globalStyle.lineHeight40}`}>还款计划：</span>
            </Col>
            <Col>
              <Table
                columns={incomeColumn}
                dataSource={finallyTableData}
                className={globalStyle.childrenTr}
                pagination={false}
                rowKey={(record) => {
                  return record?.amountDue + record?.principal + record?.repayTime;
                }}
              />
            </Col>
          </Row>
        </Tabs.TabPane>
        {inComeInfo?.imagingInfo && (
          <Tabs.TabPane tab="影像资料" key={TABS_ITEM.IMAGE_INFO}>
            {inComeInfo?.imagingInfo?.length > 0 ? (
              <Row className="personal-list" style={{ marginTop: 16 }}>
                {
                  <>
                    {sortArr(inComeInfo?.imagingInfo, 'imagingInfo')?.map(
                      (
                        item: {
                          fieldValue: string;
                          fieldDesc: string;
                          fieldCode: string;
                        },
                        index: number,
                      ) => {
                        return getFuncDom('imagingInfo', item, index);
                      },
                    )}
                  </>
                }
              </Row>
            ) : (
              '暂无数据'
            )}
          </Tabs.TabPane>
        )}
      </Tabs>
    </Card>
  );
};

export default IncomingInfo;
