/*
 * @Author: your name
 * @Date: 2020-11-23 16:54:48
 * @LastEditTime: 2025-04-11 15:09:09
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/data.d.ts
 */

export interface OrderListItem {
  phone: any;
  idNo: ReactNode | { children: ReactNode; props: any };
  applyAmount: number;
  createdAt: string;
  id: number;
  interestRate: number;
  loanTerm: string;
  orderNo: string;
  orderStatus: number;
  productName: string;
  repayType: string;
  userName: string;
  userNo: string;
  firstPayRateStr: string;
  firstPayAmount: number;
  guaranteeType: number | string;
  isTimeoutFlag: number | string;
  sendMsgStatus?: number;
  address?: string;
  orderReceiptNo?: string;
  activateOrderNo?: string;
  applySource?: number;
  creditStatus?: number;
  approvalFailDesc?: string;
  approvalFailReason?: string;
  channelStatus?: number;
  repurchaseStatus?: number;
  funderChannelCode?: string;
  licenseType?: string;
  creditAmount?: number;
  funderCode?: string;
  externalUserId?: string | null;
}

export interface OrderListParams {
  applyTimeEnd?: string;
  applyTimeStart?: string;
  current?: number;
  idNo?: string;
  orderNo?: string;
  orderStatus?: number;
  pageSize?: number;
  phone?: string;
  userNo?: string;
}
export interface OrderListPagination {
  total: number;
  pageSize: number;
  current: number;
}

export interface OrderListData {
  list: OrderListItem[];
  pagination: Partial<OrderListPagination>;
}

export interface LeaseFeeItem {
  deductAmount: number;
  deductDesc: string;
  feeStatus: number;
  feeType: number;
  id: number;
  orderNo: string;
  payAmount: number;
  payDate: string;
  payType: number;
  payee: string;
  payer: string;
  returnFlag: number;
  userNo: string;
}

export interface RepayItem {
  amountDue: number;
  cost: number;
  interest: number;
  principal: number;
  repayTime: string;
  termDetail: string;
  children?: RepayItem[];
  status: number;
  channelStatus?: number;
  funderChannelCode?: string;
  term?: string;
  advancedStatus: number;
}

export interface BindCarParams {
  carUniqueCode?: string;
  engineCode?: string;
  orderNo?: string;
  licenseCode?: number;
  remark?: string;
  totalPrice?: string;
  modifyFields?: any[]; // 存渠道账号修改的字段名, 如果modifyFields为空或者只有“车架号”，则跳过审批，翻转成绑车成功
}

export interface CheckCarParams {
  carModelCode: string;
  productCode: string;
  carNo: string;
  carMakeDate: string;
  carUniqueCode: string;
  channelCode: string;
}

export interface DeliveryCarParams {
  carDeliveryReceipt: [];
  groupPhoto: [];
  orderNo: string;
  remark?: string;
  userNo: string;
}

export interface SaveDeliveryInfoParams {
  frameNumberPhoto: string;
  groupPhotoOfPeopleAndVehicles: string;
  otherDeliveryDataPhotoList?: any[];
  licenseCode?: string;
  orderNo: string;
  vinOcrInfo?: {
    vinMatched: boolean;
    vinCode: string;
    validVin: boolean;
  } | null;
}

export interface BindCarInfo {
  carType: number;
  carUniqueCode: string;
  channelId?: string;
  licenseCode: string;
  licenseType: number;
  engineCode: string;
  bindCarAuditLogs: [];
  bindCarStatus: number;
  isNewCarDelivery: number;
  carFabricateDate: string;
  bindCarDate: string;
  carDeliveryInfo: {
    auditLogs: [];
    auditStatus: number;
    carDeliveryReceipt: [];
    groupPhoto: [];
    remark: string;
  };
  newCarDeliveryInfo: {
    auditLogs: [];
    otherDeliveryDataPhoto: any[];
    frameNumberPhoto: string;
    groupPhotoOfPeopleAndVehicles: string;
    groupPhotoOfPeopleAndVehicles: [];
    remark: string;
  };
  // remark: string;
  bindRemark: string;
  licenseCompany: string;
  licenseCompanyId: string;
  applyStore: string;
  applyStoreId: string;
  modifyFields?: any;
  carRegistrationFiles: {
    filePath: string;
    name: string;
    uid: string;
    url: string;
  }[];
  drivingLicenseFiles: {
    filePath: string;
    name: string;
    uid: string;
    url: string;
  }[];
  // carInfo:{
  //   remark: string;
  //   carUniqueCode: string;
  //   licenseCode: string;
  //   engineCode: string;
  // }
}

export interface OrderInfoParams {
  orderNo?: string;
}

export interface OrderInfo {
  orderAmount: string;
  orderChannel: string;
  orderNo: string;
  orderTime: string;
  productName: string;
  status: number;
  userName: string;
  userNo: string;
}

export interface ListRspList {
  amountDue: number;
  cost: number;
  interest: number;
  principal: number;
  repayTime: string;
}

export interface RepayInfo {
  listRspList: ListRspList[];
  repayMode: number;
  repayTerm: number;
}

export interface RepayRegistItem {
  actualRepaymentAmount: number;
  cost: number;
  interest: number;
  penaltyInterest: number;
  principal: number;
  recordingNo: string;
  repayTime: string;
  repayType: string;
}

export interface ProductNameList {
  code: string;
  desc: string;
}

export interface DebtItem {
  dealTime: string;
  loadFromName: string;
  loadToName: string;
  loanAmount: number;
  startingTime: string;
  status: number;
  conversionNo: string;
}

export interface OtherDeliveryParams {
  orderNo: string;
  otherDeliveryDataPhotoList: [];
  remark: string;
}

export interface SubmitOtherDeliveryParams
  extends Omit<OtherDeliveryParams, 'otherDeliveryDataPhotoList'> {
  otherDeliveryDataPhotoList: any[];
  groupPhotoOfPeopleAndVehicles: string;
  frameNumberPhoto: string;
}

export interface NewDeliveryAuditParams {
  operation: boolean;
  orderNo: string;
  rejectMsg?: string;
}

export interface RegistList {
  applyName?: string;
  createdAt?: string;
  remark?: string;
  remitAmount?: string;
  remitType?: string;
  status: number;
  attach?: { netWorkPath: string; name: string }[];
}

export type CarOcrParams = {
  filePath: string;
  orderNo: string;
};

export type BankList = {
  bankName?: string;
  cardNo?: string;
  phone?: string;
  updatedAt?: string;
  type: number;
};
export interface AuidtInfo {
  orderNo: string;
  status: number;
  info: { creditAmount: string };
  confirmCallback: function;
  deratingRentPrice?: string;
}
export interface AuditParams {
  confirmResult: boolean;
  orderNo: string;
}

export type SendMsgListInfo = {
  accordNum: number;
  orderList: string[];
};

export interface ShouldRepayItem {
  termDetail?: string;
  status: number;
  repayTime?: string;
  shouldAmountDue: number;
  shouldPrincipal: number;
  shouldInterest: number;
  overdueInterest: number;
  otherCost: number;
  repayPlanNo: string;
  remainingAmountDue: number;
  term?: number;
  latePaymentFee?: number;
  paidPrincipal: number;
  paidInterest: number;
}
export interface RepayEditItem {
  id: number | string;
  costType?: number;
  remissionPeriod?: number[];
}
export interface OffLineParams {
  actualRepayAmount?: number; //实际还款金额
  actualRepayRole?: number; //实际还款方枚举
  applyUserId?: string; //提交人
  compensateOrgName?: string; //代偿机构名称
  compensateType?: number; //代偿方式
  costDetail?: {
    costType: number;
    id: number;
    remissionAmount: number;
    remissionTerms: [];
  }[]; //减免金额明细
  freezeAmount?: number; //还款中金额
  isAllowJumpTerm?: boolean; //是否支持跳期还款
  isFullAmount?: boolean; //是否足额还款
  isRemission?: boolean; //是否需要减免
  orderNo?: string; //订单编号
  productCode?: string; //产品code
  repayDate?: string; //还款日期
  repayPlayNos?: []; //还款计划编号
  totalRemissionAmount?: number; //总减免金额
  totalRepayAmount?: number; //应还总金额
  userName?: string; //用户名称
  userNo?: string; //用户编号
  attach?: { filePath: string; name: string }[]; //附件
  repayBankNo?: string; //  银行卡号
  thirdFlowId?: string; //  三方流水号
  repayChannel?: string; //  还款渠道
}

export type ThreeVerifyData = {
  idNo: string;
  name: string;
  phone: string;
};

export type CarInfoParamsV2 = {
  orderNo: string;
  carId: string;
  channelId: string;
  type: number;
};

export type PriceParams = {
  applyCityId: string; // 申请城市
  carId: string; // 汽车id
  type: number; // 1.新车 2. 二手车
};

export type ReCalcParams = {
  userNo: string;
  downPaymentPrice?: number; //首付款金额
  downPaymentProportion?: number; //首付款比例
  numberOfLeasePeriods: number; //租赁期数
  productCode: string;
  dealPrice: number;
  transferPrice?: number;
  carTypeCode?: number;
};

export type RentCarPlanParams = {
  carInfo?: Record<string, any>;
  financePlan?: Record<string, any>;
  orderNo: string;
  userNo: string;
  remark?: string;
};

export type TempParams = {
  orderNo: string;
  first: string;
  second: string;
  remark?: string;
};

export type FinalRiskParams = {
  orderNo: string;
  riskFieldList: { param: any; riskKey: string }[];
};

export type OrderDetailItem = {
  idNo?: number;
  userName?: string;
  phone?: string;
  userNo?: string;
  signQrCodeUrl?: string;
};

export type UserSchemeParams = {
  orderNo: string;
  productCode: string;
  transferPrice: number;
  dealPrice: string;
  carType: number;
};

export type submitRiskPatchParams = {
  riskEnterFieldReqList: any[];
  riskSupplementReqList: any[];
  riskEnterFieldListId: number;
};

export type OperateLogParams = {
  orderNo: string;
  current: number;
  pageSize: number;
};

export type ErrorDataListItem = {
  id: number;
  userNo: string;
  orderNo: string;
  channelOrderNo: string;
  repayPlanNo: string;
  channelPlanNo: string;
  status: number;
  channelStatus: number;
  repayTime: string;
  channelTime: string;
  term: number;
  repayAmount: string;
  lendingTime: string;
  repayRecordingTime: string;
  createdAt: string;
  orderStatus: string;
  beginRepayRecordingTime: string;
  contrast: string;
};

export type ErrorDataParams = {
  pageSize?: number;
  pageNum?: number;
  createdAt?: string;
};
