/**
 *  @name 流水明细
 *
 */
import { getWalletAccountInfo } from '@/components/WalletAccountInfo/services';
import { EyeInvisibleTwoTone, EyeTwoTone } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Button, message, Modal } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import { getTransationList } from '../../services';
import './index.less';
interface TransactionDetailsProps {
  transactionRef: React.RefObject<any>;
  externalOwnerId: string | undefined;
  secondProductCode: '0303' | '0201'; // 0303 车险， 0201 融租
}

const TransactionDetailModal: React.FC<TransactionDetailsProps> = (props) => {
  const { transactionRef, externalOwnerId, secondProductCode } = props;

  const [open, setOpen] = useState(false);
  const [showEyeIcon, setShowEyeIcon] = useState(false);
  const [isGetBalance, setIsGetBalance] = useState(false);
  const [loading, setLoading] = useState(false);
  const [welletInfo, setWelletInfo] = useState<any>(null);

  useImperativeHandle(transactionRef, () => ({
    show: () => {
      setOpen(true);
    },
    hide: () => {
      setOpen(false);
    },
  }));

  const columns: ProColumns<any>[] = [
    {
      title: '金额',
      dataIndex: 'amount',
      valueType: 'money',
    },
    {
      title: '操作类型',
      dataIndex: 'bizTypeName',
      render: (_, record) => {
        return <span>{`${record.bizTypeName} - ${record.subBizTypeName}`}</span>;
      },
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
    },
    {
      title: '银行流水号/业务单号',
      dataIndex: 'flowNo',
    },
  ];
  // 获取余额
  const getBalance = () => {
    if (!externalOwnerId) {
      message.error('专属充值账户不存在');
      return;
    }
    setLoading(true);
    getWalletAccountInfo({ externalOwnerId, secondProductCode })
      .then((res) => {
        setWelletInfo(res?.data);
        setIsGetBalance(true);
        setShowEyeIcon(true);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  async function request(params) {
    if (!externalOwnerId) {
      return {
        data: [],
        total: 0,
        success: true,
      };
    }
    const res = await getTransationList({
      ...params,
      externalOwnerId,
      secondProductCode,
    });
    return {
      data: res?.data,
      total: res?.total,
      success: true,
    };
  }

  return (
    <Modal
      key="transactionDetailModal"
      title="钱包流水"
      width={1000}
      open={open}
      cancelText="关闭"
      okButtonProps={{
        style: {
          display: 'none',
        },
      }}
      onCancel={() => setOpen(false)}
      closable={false}
      destroyOnClose
    >
      <ProTable
        className="transaction-list"
        columns={columns}
        search={false}
        toolBarRender={false}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        request={request}
        pagination={{
          position: ['topRight'],
          className: 'transaction-list-pagination',
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 10,
          showTotal: (total) => (
            <div
              className="transaction-list-header"
              style={{ display: 'flex', alignItems: 'center' }}
            >
              <div className="float_info">
                <span>钱包ID: {externalOwnerId ?? ''}</span>
                <span style={{ margin: '0 16px' }}>&nbsp;</span>
                <span>
                  <span>
                    当前余额:{' '}
                    {isGetBalance && showEyeIcon ? `¥${welletInfo?.totalAmount}` : '******'}
                  </span>
                  <Button
                    type="link"
                    loading={loading}
                    className="eye-icon"
                    onClick={() => {
                      if (!isGetBalance) {
                        getBalance();
                      }
                      if (isGetBalance) {
                        setShowEyeIcon(!showEyeIcon);
                      }
                    }}
                  >
                    {showEyeIcon ? <EyeTwoTone /> : <EyeInvisibleTwoTone />}
                  </Button>
                </span>
              </div>
              <span style={{ margin: '0 16px' }}>&nbsp;</span>
              <span>总共 {total} 条</span>
            </div>
          ),
        }}
      />
    </Modal>
  );
};

export default React.memo(TransactionDetailModal);
