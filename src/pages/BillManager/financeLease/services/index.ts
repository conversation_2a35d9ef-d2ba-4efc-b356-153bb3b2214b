import type { IcarChannelItem } from '@/pages/CarInsurance/type';
import { bizadminHeader } from '@/services/consts';
import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  IbillInfo,
  IbillInfoParams,
  IbillListItem,
  IbillListParams,
  IsearchUserListItem,
  IsubmitBillRepay,
  IuserType,
} from '../types';

/**
 * 获取所有的渠道信息
 * @returns
 */
export async function getChannelInfo(): Promise<IcarChannelItem[]> {
  const data = await request(`/bizadmin/channel/list`, {
    method: 'GET',
    params: {
      pageNumber: 1,
      pageSize: 10000,
    },
    ifTrimParams: true,
    headers,
  });
  return data?.data;
}

/**
 * 模糊搜搜用户名称
 * @returns
 */
export async function searchUserList(params: {
  certiName?: string;
  current: number;
  pageSize: number;
  userType: IuserType;
  productSecondCode: string;
}): Promise<IsearchUserListItem[]> {
  const data = await request(`/bizadmin/bill/searchUserList`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data?.data;
}

/**
 *
 * @returns
 */
export async function billList(
  params: IbillListParams,
): Promise<{ total: number; data: IbillListItem[] }> {
  const data = await request(`/bizadmin/bill/billList`, {
    method: 'POST',
    data: params,
    headers,
    ifTrimParams: true,
  });
  return data;
}

/**
 *
 * @returns
 */
export async function billInfo(params: IbillInfoParams): Promise<IbillInfo> {
  const data = await request(`/bizadmin/bill/billInfo`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data;
}

/**
 *
 * @returns
 */
export async function submitBillRepay(params: IsubmitBillRepay) {
  const data = await request(`/bizadmin/bill/submitBillRepay`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data;
}

/**
 *  提前结清
 * @returns
 */
export async function billSettleInfo(params: IbillInfoParams): Promise<IbillInfo> {
  const data = await request(`/bizadmin/bill/billInfo/settle`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data;
}

/**
 *  提前结清
 * @returns
 */
export async function submitBillSettleRepay(params: IsubmitBillRepay) {
  const data = await request(`/bizadmin/bill/submitBillSettle`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data;
}

/**
 * 车险 - 订单账期 和 车辆期账的导出 - 异步
 */

export async function billExport(params: any) {
  return request(`/bizadmin/bill/billExport`, {
    headers: bizadminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}

// 预备退保
export async function insuranceExit(data: {
  expectCancel?: boolean;
  orderNo?: string;
  subjectUniqueNo?: string;
}) {
  return request(`/bizadmin/bill/cancelCarInsurance`, {
    headers: bizadminHeader,
    method: 'POST',
    data,
  });
}

// 还款
export async function calculateBill(data: {
  billInfoList: any[];
  remissionList: [];
  repayAmount: number;
}) {
  return request(`/bizadmin/bill/calculateBill`, {
    headers: bizadminHeader,
    method: 'POST',
    data,
    skipGlobalErrorTip: true,
  });
}

// 获取融租渠道专属账号信息
export async function getChannelAccountInfo(params: { channelCode: string }) {
  return request(`/bizadmin/channel/detail`, {
    method: 'GET',
    params,
    headers: bizadminHeader,
  });
}
